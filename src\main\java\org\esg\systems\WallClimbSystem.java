package org.esg.systems;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.inventory.ItemStack;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.esg.Main;
import org.esg.utils.MessageHandler;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.logging.Logger;

/**
 * Sistema de escalada que permite aos jogadores escalar paredes.
 * Ativado quando o jogador usa shift + botão direito próximo a uma parede.
 */
public class WallClimbSystem implements Listener {
    private static final Logger LOGGER = Logger.getLogger(WallClimbSystem.class.getName());
    private final Main plugin;
    private final Set<UUID> climbingPlayers = new HashSet<>();
    private final Map<UUID, Vector> climbingDirection = new HashMap<>();
    private final Map<UUID, Long> lastClimbStartTime = new HashMap<>();
    private final Map<UUID, Integer> climbTicks = new HashMap<>();

    private static final long CLIMB_TOGGLE_COOLDOWN_MS = 500;
    private static final int MAX_CLIMB_TICKS = 100; // 5 segundos

    /**
     * Construtor do sistema de escalada.
     */
    public WallClimbSystem(Main plugin) {
        this.plugin = plugin;
        new BukkitRunnable() {
            @Override
            public void run() {
                processClimbingPlayers();
            }
        }.runTaskTimer(plugin, 0L, 1L);
        LOGGER.info("WallClimbSystem initialized");
    }

    /**
     * Processa todos os jogadores que estão escalando.
     */
    private void processClimbingPlayers() {
        Set<UUID> playersToRemove = new HashSet<>();

        for (UUID playerId : climbingPlayers) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                playersToRemove.add(playerId);
                continue;
            }

            // Incrementar contador e verificar limite de tempo
            int ticks = climbTicks.getOrDefault(playerId, 0) + 1;
            climbTicks.put(playerId, ticks);
            if (ticks >= MAX_CLIMB_TICKS) {
                stopClimbing(player);
                playersToRemove.add(playerId);
                continue;
            }

            // Verificar se está no chão (com atraso para permitir pulo)
            if (player.isOnGround() && ticks > 20 && player.getVelocity().getY() <= 0) {
                stopClimbing(player);
                playersToRemove.add(playerId);
                continue;
            }

            // Verificar proximidade da parede
            Vector wallDir = climbingDirection.get(playerId);
            boolean stillNearWall = false;

            if (wallDir != null) {
                Location playerLoc = player.getLocation();
                for (double d = 0.5; d <= 1.0; d += 0.25) {
                    Vector offset = wallDir.clone().multiply(-d);
                    Block checkBlock = playerLoc.clone().add(offset).getBlock();
                    if (checkBlock.getType().isSolid()) {
                        stillNearWall = true;
                        break;
                    }
                }
                if (!stillNearWall) {
                    stillNearWall = isPlayerNearAnySolidBlock(player);
                }
            } else {
                stillNearWall = isPlayerNearAnySolidBlock(player);
            }

            // Parar se afastou da parede
            if (!stillNearWall) {
                stopClimbing(player);
                playersToRemove.add(playerId);
                continue;
            }

            // Verificamos apenas se o jogador está muito longe da parede
            if (!isVeryCloseToWall(player) && !isPlayerNearAnySolidBlock(player)) {
                stopClimbing(player);
                playersToRemove.add(playerId);
                continue;
            }

            // Aplicar efeitos
            applyClimbingEffects(player);
        }

        climbingPlayers.removeAll(playersToRemove);
    }

    /**
     * Verifica se um jogador está próximo de uma parede.
     */
    private boolean isNearWall(Player player) {
        return detectWallDirection(player);
    }

    /**
     * Verifica se o jogador está próximo de qualquer bloco sólido.
     */
    private boolean isPlayerNearAnySolidBlock(Player player) {
        Location playerLoc = player.getLocation();

        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0) continue;
                if (Math.abs(x) == 1 && Math.abs(z) == 1) continue; // Pular diagonais

                for (int y = -1; y <= 1; y++) {
                    Block block = playerLoc.getBlock().getRelative(x, y, z);
                    if (block.getType().isSolid()) {
                        Location blockCenter = block.getLocation().add(0.5, 0.5, 0.5);
                        double distance = playerLoc.distance(blockCenter);
                        if (distance <= 1.2) return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Aplica efeitos contínuos ao jogador que está escalando.
     */
    private void applyClimbingEffects(Player player) {
        UUID playerId = player.getUniqueId();

        // Habilitar voo temporário
        if (!player.getAllowFlight()) {
            player.setAllowFlight(true);
            player.setFlying(true);
        }

        // Verificar direção da parede
        Vector wallDirection = climbingDirection.get(playerId);
        if (wallDirection == null) {
            if (!detectWallDirection(player)) {
                stopClimbing(player);
                return;
            }
            wallDirection = climbingDirection.get(playerId);
        }

        // Verificar proximidade da parede
        boolean nearWall = false;
        Location playerLoc = player.getLocation();

        for (double d = 0.5; d <= 1.5; d += 0.5) {
            Block checkBlock = playerLoc.clone().add(wallDirection.clone().multiply(-d)).getBlock();
            if (checkBlock.getType().isSolid()) {
                nearWall = true;
                break;
            }
        }

        if (!nearWall) {
            nearWall = isPlayerNearAnySolidBlock(player);
            if (!nearWall) {
                stopClimbing(player);
                return;
            }
        }

        // Aplicar efeitos
        player.setFlySpeed(0.03f);
        player.setFallDistance(0);

        // Efeitos sonoros
        if (player.getTicksLived() % 10 == 0) {
            player.playSound(player.getLocation(), Sound.STEP_STONE, 0.1f, 1.0f);
        }

        // Barra de progresso
        int remainingTicks = MAX_CLIMB_TICKS - climbTicks.getOrDefault(playerId, 0);
        double percentRemaining = (double) remainingTicks / MAX_CLIMB_TICKS;
        StringBuilder bar = new StringBuilder("§6Escalando... §7[");
        int barLength = 20;
        int filledBars = (int) Math.ceil(percentRemaining * barLength);

        for (int i = 0; i < filledBars; i++) bar.append("§a|");
        for (int i = filledBars; i < barLength; i++) bar.append("§c|");
        bar.append("§7]");

        MessageHandler.sendActionBar(player, bar.toString());
    }

    /**
     * Detecta a direção da parede que o jogador está escalando.
     */
    private boolean detectWallDirection(Player player) {
        Location playerLoc = player.getLocation();
        Vector playerDir = player.getLocation().getDirection().setY(0).normalize();

        // Não permitir escalar olhando muito para baixo
        if (player.getLocation().getPitch() > 60) return false;

        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0) continue;

                for (int y = -1; y <= 1; y++) {
                    Block block = playerLoc.getBlock().getRelative(x, y, z);

                    if (block.getType().isSolid() && !(x == 0 && z == 0 && y == -1)) {
                        Vector direction = new Vector(x, 0, z).normalize();
                        double dot = playerDir.dot(direction);

                        if (dot < 0.7) {
                            climbingDirection.put(player.getUniqueId(), direction);
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Verifica se o jogador está muito próximo de uma parede.
     */
    private boolean isVeryCloseToWall(Player player) {
        Location playerLoc = player.getLocation();

        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0) continue;

                for (int y = 0; y <= 1; y++) {
                    Block block = playerLoc.getBlock().getRelative(x, y, z);
                    if (block.getType().isSolid()) {
                        Location blockCenter = block.getLocation().add(0.5, 0.5, 0.5);
                        if (playerLoc.distance(blockCenter) <= 1.0) return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Inicia a escalada para um jogador.
     */
    public void startClimbing(Player player) {
        UUID playerId = player.getUniqueId();
        if (climbingPlayers.contains(playerId)) return;

        climbingPlayers.add(playerId);
        climbTicks.put(playerId, 0);
        lastClimbStartTime.put(playerId, System.currentTimeMillis());

        player.setAllowFlight(true);
        player.setFlying(true);
        player.setFlySpeed(0.03f);
        player.setFallDistance(0);

        player.playSound(player.getLocation(), Sound.ITEM_PICKUP, 0.5f, 1.2f);
    }

    /**
     * Encerra a escalada para um jogador.
     */
    public void stopClimbing(Player player) {
        UUID playerId = player.getUniqueId();

        climbingPlayers.remove(playerId);
        climbTicks.remove(playerId);
        climbingDirection.remove(playerId);
        lastClimbStartTime.remove(playerId);

        if (player.isFlying()) player.setFlying(false);
        player.setAllowFlight(false);
        player.setFlySpeed(0.1f);
        player.setFallDistance(0);

        player.playSound(player.getLocation(), Sound.ITEM_BREAK, 0.5f, 0.8f);
    }

    /**
     * Manipula o evento de clique para encerrar a escalada.
     * Agora requer shift + botão direito para desativar.
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onPlayerClickToStopClimbing(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        if (!climbingPlayers.contains(playerId)) return;
        if ((event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) || !player.isSneaking()) return;

        ItemStack item = player.getItemInHand();
        if (item != null && item.getType() != Material.AIR) return;

        // Verificar cooldown
        long currentTime = System.currentTimeMillis();
        Long startTime = lastClimbStartTime.get(playerId);
        if (startTime != null && (currentTime - startTime) < CLIMB_TOGGLE_COOLDOWN_MS) {
            event.setCancelled(true);
            return;
        }

        stopClimbing(player);
        event.setCancelled(true);
    }

    /**
     * Manipula o evento de interação para iniciar a escalada.
     * Agora requer shift + botão direito para ativar.
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.isCancelled()) return;

        Player player = event.getPlayer();
        if (climbingPlayers.contains(player.getUniqueId())) return;

        // Verificar se é shift + botão direito
        if ((event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) || !player.isSneaking()) return;

        ItemStack item = player.getItemInHand();
        if (item != null && item.getType() != Material.AIR) return;

        // Verificar se clicou no chão
        if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            Block clickedBlock = event.getClickedBlock();

            if (player.getLocation().getPitch() > 60) return;

            Location playerLoc = player.getLocation();
            if (clickedBlock.equals(playerLoc.getBlock().getRelative(0, -1, 0))) return;
        }

        // Verificar se está próximo de uma parede
        if (!detectWallDirection(player)) return;

        startClimbing(player);
        event.setCancelled(true);
    }

    /**
     * Manipula o evento de movimento do jogador.
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        if (!climbingPlayers.contains(playerId)) return;

        // Verificar se tocou o chão
        if (player.isOnGround()) {
            int ticks = climbTicks.getOrDefault(playerId, 0);
            if (ticks > 20 && player.getVelocity().getY() <= 0) {
                stopClimbing(player);
                return;
            }
        }

        // Verificar se afastou da parede
        if (!isPlayerNearAnySolidBlock(player)) {
            stopClimbing(player);
        }
    }
}
