package org.esg.utils;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.esg.models.Weapon;

/**
 * Classe utilitária para gerenciar atualizações e recuperação de itens relacionados a armas.
 */
public final class WeaponUtils {
    private WeaponUtils() {
        // Construtor privado para evitar instanciação
    }

    /**
     * Atualiza a arma na mão do jogador.
     */
    public static void updateWeaponInHand(Player player, Weapon weapon) {
        ItemStack item = player.getInventory().getItemInHand();
        if (item != null && NBTUtils.getWeaponID(item) != null) {
            ItemStack updatedItem = NBTUtils.applyWeaponNBT(item, weapon, player);
            player.getInventory().setItemInHand(updatedItem);
        }
    }
    
    /**
     * Atualiza apenas a munição da arma na mão do jogador sem causar animação.
     * Este método modifica o NBT diretamente sem substituir o item.
     * 
     * @param player O jogador
     * @param currentAmmo Nova quantidade de munição
     * @return true se a atualização foi bem-sucedida, false caso contrário
     */
    public static boolean updateAmmoInHand(Player player, int currentAmmo) {
        ItemStack item = player.getInventory().getItemInHand();
        if (item != null && NBTUtils.getWeaponID(item) != null) {
            NBTUtils.updateAmmoOnly(item, currentAmmo);
            return true;
        }
        return false;
    }

    /**
     * Atualiza a arma em um slot específico do inventário do jogador.
     */
    public static void updateWeaponInSlot(Player player, int slot, Weapon weapon) {
        ItemStack item = player.getInventory().getItem(slot);
        if (item != null) {
            ItemStack updatedItem = NBTUtils.applyWeaponNBT(item, weapon, player);
            player.getInventory().setItem(slot, updatedItem);
        }
    }

    /**
     * Obtém uma arma a partir de um item.
     */
    public static Weapon getWeaponFromItem(ItemStack item, Player player) {
        return NBTUtils.getWeaponFromNBT(item, player);
    }

    /**
     * Obtém o ID da arma a partir de um item.
     */
    public static String getWeaponId(ItemStack item) {
        return NBTUtils.getWeaponID(item);
    }

    /**
     * Aplica uma arma a um item.
     */
    public static ItemStack applyWeaponToItem(ItemStack item, Weapon weapon, Player player) {
        return NBTUtils.applyWeaponNBT(item, weapon, player);
    }

    /**
     * Verifica se um item é a mesma arma com base no ID.
     */
    public static boolean isSameWeapon(ItemStack item, String weaponId) {
        if (item == null || weaponId == null) return false;
        String itemWeaponId = getWeaponId(item);
        return weaponId.equals(itemWeaponId);
    }
}