package org.esg.ui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.esg.Main;
import org.esg.Manager.ArmorManager;
import org.esg.enums.ArmorPiece;
import org.esg.models.Armor;

import java.util.ArrayList;
import java.util.List;

/**
 * Menu de gerenciamento de armaduras.
 * Permite que os jogadores visualizem e removam armaduras facilmente.
 */
public class ArmorMenu implements Listener {

    private static final String MENU_TITLE = ChatColor.DARK_GRAY + "Gerenciamento de Armaduras";
    private static final int INVENTORY_SIZE = 27;
    private static final int REMOVE_HELMET_SLOT = 10;
    private static final int REMOVE_CHESTPLATE_SLOT = 12;
    private static final int REMOVE_LEGGINGS_SLOT = 14;
    private static final int REMOVE_BOOTS_SLOT = 16;
    private static final int REMOVE_ALL_SLOT = 22;

    /**
     * Registra o listener do menu.
     * Deve ser chamado no onEnable do plugin.
     */
    public static void register() {
        Bukkit.getPluginManager().registerEvents(new ArmorMenu(), Main.getPlugin());
    }

    /**
     * Abre o menu de armaduras para um jogador.
     * 
     * @param player O jogador
     */
    public static void open(Player player) {
        Inventory inventory = Bukkit.createInventory(null, INVENTORY_SIZE, MENU_TITLE);
        
        // Criar itens do menu
        ItemStack removeHelmet = createMenuItem(Material.DIAMOND_HELMET, 
                "§cRemover Capacete", 
                "§7Clique para remover seu capacete atual.");
        
        ItemStack removeChestplate = createMenuItem(Material.DIAMOND_CHESTPLATE, 
                "§cRemover Peitoral", 
                "§7Clique para remover seu peitoral atual.");
        
        ItemStack removeLeggings = createMenuItem(Material.DIAMOND_LEGGINGS, 
                "§cRemover Calças", 
                "§7Clique para remover suas calças atuais.");
        
        ItemStack removeBoots = createMenuItem(Material.DIAMOND_BOOTS, 
                "§cRemover Botas", 
                "§7Clique para remover suas botas atuais.");
        
        ItemStack removeAll = createMenuItem(Material.BARRIER, 
                "§c§lRemover Tudo", 
                "§7Clique para remover todas as armaduras.");
        
        // Colocar itens no inventário
        inventory.setItem(REMOVE_HELMET_SLOT, removeHelmet);
        inventory.setItem(REMOVE_CHESTPLATE_SLOT, removeChestplate);
        inventory.setItem(REMOVE_LEGGINGS_SLOT, removeLeggings);
        inventory.setItem(REMOVE_BOOTS_SLOT, removeBoots);
        inventory.setItem(REMOVE_ALL_SLOT, removeAll);
        
        // Preencher espaços vazios com vidro preto
        ItemStack filler = createMenuItem(Material.STAINED_GLASS_PANE, " ", "");
        for (int i = 0; i < INVENTORY_SIZE; i++) {
            if (inventory.getItem(i) == null) {
                inventory.setItem(i, filler);
            }
        }
        
        // Abrir o inventário para o jogador
        player.openInventory(inventory);
    }
    
    /**
     * Cria um item para o menu.
     * 
     * @param material O material do item
     * @param name O nome do item
     * @param lore A descrição do item
     * @return O item criado
     */
    private static ItemStack createMenuItem(Material material, String name, String lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        
        if (!lore.isEmpty()) {
            List<String> loreList = new ArrayList<>();
            loreList.add(lore);
            meta.setLore(loreList);
        }
        
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * Manipula os cliques no inventário do menu.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        String inventoryTitle = event.getView().getTitle();
        
        if (!inventoryTitle.equals(MENU_TITLE)) return;
        
        event.setCancelled(true);
        
        int slot = event.getRawSlot();
        
        switch (slot) {
            case REMOVE_HELMET_SLOT:
                ArmorManager.removeArmorPiece(player, ArmorPiece.HELMET);
                player.closeInventory();
                break;
                
            case REMOVE_CHESTPLATE_SLOT:
                ArmorManager.removeArmorPiece(player, ArmorPiece.CHESTPLATE);
                player.closeInventory();
                break;
                
            case REMOVE_LEGGINGS_SLOT:
                ArmorManager.removeArmorPiece(player, ArmorPiece.LEGGINGS);
                player.closeInventory();
                break;
                
            case REMOVE_BOOTS_SLOT:
                ArmorManager.removeArmorPiece(player, ArmorPiece.BOOTS);
                player.closeInventory();
                break;
                
            case REMOVE_ALL_SLOT:
                ArmorManager.removeAllArmor(player);
                player.closeInventory();
                break;
        }
    }
} 