package org.esg.models;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.esg.enums.ArmorPiece;
import org.esg.enums.ArmorType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Representa uma peça de armadura no jogo.
 * Existem dois tipos principais: Leather (couro) e Kevlar (diamante).
 */
public class Armor {

    private final ArmorType type;
    private final ArmorPiece piece;
    private final String name;
    private final double damageReduction;
    private final int durability;
    private int currentDurability;

    /**
     * Cria uma nova instância de peça de armadura.
     *
     * @param type O tipo de armadura
     * @param piece A peça de armadura
     * @param name O nome da armadura
     * @param durability A durabilidade máxima da armadura
     */
    public Armor(ArmorType type, ArmorPiece piece, String name, int durability) {
        this.type = type;
        this.piece = piece;
        this.name = name;

        // Calcular a redução de dano com base no tipo e na peça
        switch (piece) {
            case HELMET:
                this.damageReduction = type.getHelmetProtection();
                break;
            case CHESTPLATE:
                this.damageReduction = type.getChestplateProtection();
                break;
            case LEGGINGS:
                this.damageReduction = type.getLeggingsProtection();
                break;
            case BOOTS:
                this.damageReduction = type.getBootsProtection();
                break;
            default:
                this.damageReduction = 0;
        }

        this.durability = durability;
        this.currentDurability = durability;
    }

    /**
     * Obtém o tipo de armadura.
     *
     * @return O tipo de armadura
     */
    public ArmorType getType() {
        return type;
    }

    /**
     * Obtém a peça de armadura.
     *
     * @return A peça de armadura
     */
    public ArmorPiece getPiece() {
        return piece;
    }

    /**
     * Obtém o nome da armadura.
     *
     * @return O nome da armadura
     */
    public String getName() {
        return name;
    }

    /**
     * Obtém a redução de dano da armadura.
     *
     * @return A redução de dano (0.0 a 1.0)
     */
    public double getDamageReduction() {
        return damageReduction;
    }

    /**
     * Obtém a durabilidade máxima da armadura.
     *
     * @return A durabilidade máxima
     */
    public int getDurability() {
        return durability;
    }

    /**
     * Obtém a durabilidade atual da armadura.
     *
     * @return A durabilidade atual
     */
    public int getCurrentDurability() {
        return currentDurability;
    }

    /**
     * Define a durabilidade atual da armadura.
     *
     * @param currentDurability A nova durabilidade atual
     */
    public void setCurrentDurability(int currentDurability) {
        this.currentDurability = Math.max(0, Math.min(currentDurability, durability));
    }

    /**
     * Reduz a durabilidade da armadura.
     *
     * @param amount A quantidade a reduzir
     * @return true se a armadura ainda tem durabilidade, false se quebrou
     */
    public boolean reduceDurability(int amount) {
        currentDurability = Math.max(0, currentDurability - amount);
        return currentDurability > 0;
    }

    /**
     * Verifica se a armadura está quebrada.
     *
     * @return true se a armadura está quebrada, false caso contrário
     */
    public boolean isBroken() {
        return currentDurability <= 0;
    }

    /**
     * Calcula o dano reduzido por esta peça de armadura.
     *
     * @param damage O dano original
     * @return O dano reduzido
     */
    public double reduceDamage(double damage) {
        // As armaduras são inquebráveis, então não verificamos se está quebrada
        // e não reduzimos a durabilidade

        // Calcular o dano reduzido
        return damage * (1.0 - damageReduction);
    }

    /**
     * Cria um item de armadura para o jogador.
     *
     * @return O item de armadura
     */
    public ItemStack createItem() {
        Material material = piece.getMaterial(type);

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName("§6" + name);

        List<String> lore = new ArrayList<>();
        lore.add("§7Tipo: " + type.getDisplayName());
        lore.add("§7Peça: " + piece.getDisplayName());
        lore.add("§7Proteção: §a" + String.format("%.0f%%", damageReduction * 100));
        lore.add("§7Inquebrável");

        meta.setLore(lore);
        meta.spigot().setUnbreakable(true); // Tornar o item inquebrável no Spigot
        item.setItemMeta(meta);

        return item;
    }

    /**
     * Equipa a armadura no jogador.
     *
     * @param player O jogador
     */
    public void equip(Player player) {
        ItemStack armorItem = createItem();

        switch (piece) {
            case HELMET:
                player.getInventory().setHelmet(armorItem);
                break;
            case CHESTPLATE:
                player.getInventory().setChestplate(armorItem);
                break;
            case LEGGINGS:
                player.getInventory().setLeggings(armorItem);
                break;
            case BOOTS:
                player.getInventory().setBoots(armorItem);
                break;
        }
    }

    /**
     * Calcula o dano reduzido por um conjunto completo de armaduras.
     *
     * @param armorSet O conjunto de armaduras
     * @param damage O dano original
     * @return O dano reduzido
     */
    public static double calculateReducedDamage(Map<ArmorPiece, Armor> armorSet, double damage) {
        double remainingDamage = damage;

        for (Map.Entry<ArmorPiece, Armor> entry : armorSet.entrySet()) {
            Armor armor = entry.getValue();
            if (armor != null) {
                // Como as armaduras são inquebráveis, não verificamos se estão quebradas
                remainingDamage = armor.reduceDamage(remainingDamage);
            }
        }

        return remainingDamage;
    }
    
    /**
     * Calcula a redução de dano adicional com base nos encantamentos de PROJECTILE_PROTECTION
     * nas peças de armadura equipadas pelo jogador.
     *
     * @param player O jogador
     * @param damage O dano original
     * @return O dano reduzido após considerar os encantamentos de proteção contra projéteis
     */
    public static double calculateProjectileProtectionReduction(Player player, double damage) {
        // Os fatores são baseados na implementação padrão do Minecraft
        // Cada nível de PROJECTILE_PROTECTION reduz aproximadamente 8% do dano por projéteis por peça
        double damageReduction = 0;
        
        // Verificar encantamento em cada peça de armadura
        ItemStack helmet = player.getInventory().getHelmet();
        if (helmet != null && helmet.containsEnchantment(Enchantment.PROTECTION_PROJECTILE)) {
            int level = helmet.getEnchantmentLevel(Enchantment.PROTECTION_PROJECTILE);
            damageReduction += level * 0.08; // 8% por nível
        }
        
        ItemStack chestplate = player.getInventory().getChestplate();
        if (chestplate != null && chestplate.containsEnchantment(Enchantment.PROTECTION_PROJECTILE)) {
            int level = chestplate.getEnchantmentLevel(Enchantment.PROTECTION_PROJECTILE);
            damageReduction += level * 0.08; // 8% por nível
        }
        
        ItemStack leggings = player.getInventory().getLeggings();
        if (leggings != null && leggings.containsEnchantment(Enchantment.PROTECTION_PROJECTILE)) {
            int level = leggings.getEnchantmentLevel(Enchantment.PROTECTION_PROJECTILE);
            damageReduction += level * 0.08; // 8% por nível
        }
        
        ItemStack boots = player.getInventory().getBoots();
        if (boots != null && boots.containsEnchantment(Enchantment.PROTECTION_PROJECTILE)) {
            int level = boots.getEnchantmentLevel(Enchantment.PROTECTION_PROJECTILE);
            damageReduction += level * 0.08; // 8% por nível
        }
        
        // Limitar a redução a 50% no máximo para balancear a proteção
        damageReduction = Math.min(damageReduction, 0.5);
        
        // Aplicar a redução de dano
        return damage * (1.0 - damageReduction);
    }
}
