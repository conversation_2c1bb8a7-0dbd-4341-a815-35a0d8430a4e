package org.esg.utils;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;

/**
 * Gerencia sons de impacto para diferentes tipos de acertos.
 */
public class ImpactSoundManager {

    /**
     * Reproduz sons de impacto baseados no tipo de acerto.
     *
     * @param shooter O jogador que atirou
     * @param target O jogador atingido
     * @param isHeadshot Se o acerto foi na cabeça
     * @param weapon A arma usada
     */
    public static void playImpactSound(Player shooter, Player target, boolean isHeadshot, Weapon weapon) {
        // Som para o atirador
        // Removido conforme solicitado: não tocar mais SUCCESSFUL_HIT
        // if (isHeadshot && weapon.getType() == WeaponType.SNIPER) {
        //     shooter.playSound(shooter.getLocation(), Sound.SUCCESSFUL_HIT, 1.0f, 2.0f);
        // } else {
        //     shooter.playSound(shooter.getLocation(), Sound.SUCCESSFUL_HIT, 0.8f, 1.0f);
        // }
        // Som para o alvo removido conforme solicitado
        // Apenas o atirador ouve o som de acerto
    }

    /**
     * Verifica se o jogador está usando armadura.
     *
     * @param player O jogador
     * @return true se o jogador estiver usando armadura
     */
    private static boolean hasArmor(Player player) {
        ItemStack helmet = player.getInventory().getHelmet();
        ItemStack chestplate = player.getInventory().getChestplate();
        ItemStack leggings = player.getInventory().getLeggings();
        ItemStack boots = player.getInventory().getBoots();

        return helmet != null || chestplate != null || leggings != null || boots != null;
    }
}
