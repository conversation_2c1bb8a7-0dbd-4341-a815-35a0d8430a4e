package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;

public class Shotgun extends Weapon {

    public Shotgun() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis
        super("Spas-12", WeaponType.SHOTGUN, AmmoType._12GAUGE, 1.0, 20, 0.05, 1.0, 80, 8, 8, 3, 7);

        // A SPAS-12 tem:
        // - <PERSON><PERSON> base moderado (1.5)
        // - Multiplicador de proximidade: 2.0x a curta distância, 0.2x a longa distância
        // - Alcance muito menor (20 vs 80 da AK47)
        // - Precisão extremamente baixa (0.05 vs 0.25 da AK47) - Isso causa um espalhamento muito maior
        // - Velocidade de tiro muito menor (1.0 vs 4.5 da AK47)
        // - Velocidade de projétil similar (80 vs 90 da AK47)
        // - 7 projéteis por tiro (vs 1 da AK47)
        // - <PERSON><PERSON> máximo a curta distância: 3.0 (1.5 * 2.0)
        // - Dano mínimo a longa distância: 0.3 (1.5 * 0.2)
        // - Espalhamento natural em cone baseado na baixa precisão
        // - Shotgun semi-automática, ideal para combate a curta distância
        // - Espalhamento aumenta significativamente com a distância devido à baixa precisão
    }
}
