package org.esg.listeners;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.stats.StatsManager;
import org.esg.utils.WeaponUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Listener para registrar estatísticas de jogadores.
 */
public class StatsListener implements Listener {

    // Mapa para rastrear o último dano causado por um jogador
    private static final Map<UUID, DamageInfo> lastDamage = new HashMap<>();

    /**
     * Registra um jogador quando ele entra no servidor.
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        // Apenas obter as estatísticas para garantir que o jogador esteja registrado
        StatsManager.getPlayerStats(player);
    }

    /**
     * Salva as estatísticas de um jogador quando ele sai do servidor.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // As estatísticas são salvas automaticamente pelo StatsManager
    }

    /**
     * Registra um tiro disparado por um jogador.
     */
    public static void registerShot(Player player, WeaponType weaponType) {
        StatsManager.registerShotFired(player, weaponType);
    }

    /**
     * Registra um tiro acertado por um jogador.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getDamager();
        Weapon weapon = WeaponUtils.getWeaponFromItem(player.getInventory().getItemInHand(), player);

        if (weapon == null) {
            return; // Não é um dano causado por uma arma
        }

        // Registrar o tiro acertado
        StatsManager.registerShotHit(player);

        // Armazenar informações sobre o dano para uso posterior (em caso de morte)
        if (event.getEntity() instanceof LivingEntity) {
            LivingEntity target = (LivingEntity) event.getEntity();
            boolean isHeadshot = false;

            // Verificar se a arma é do tipo sniper - apenas snipers podem causar headshots
            if (weapon.getType() == WeaponType.SNIPER) {
                // Verificar se foi um headshot (usando a altura do dano)
                // Como getEyeHeight() não está disponível para todas as entidades no Bukkit 1.8,
                // usamos uma estimativa baseada na altura da entidade
                double entityHeight = 1.8; // Altura padrão para a maioria das entidades
                if (event.getEntity() instanceof Player) {
                    entityHeight = ((Player) event.getEntity()).getEyeHeight() * 2;
                }

                double damageHeight = event.getEntity().getLocation().getY() + (entityHeight * 0.8); // Aproximadamente na altura da cabeça
                double playerHeight = player.getLocation().getY() + player.getEyeHeight();

                if (Math.abs(damageHeight - playerHeight) < 0.5) {
                    isHeadshot = true;
                }
            }

            // Armazenar informações sobre o dano
            lastDamage.put(target.getUniqueId(), new DamageInfo(player.getUniqueId(), weapon.getType(), isHeadshot));
        }
    }

    /**
     * Registra um kill quando uma entidade morre.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();

        if (!(entity instanceof Player)) {
            return; // Apenas registrar mortes de jogadores
        }

        Player victim = (Player) entity;

        // Registrar a morte do jogador
        StatsManager.registerDeath(victim);

        // Verificar se o jogador foi morto por outro jogador
        DamageInfo damageInfo = lastDamage.get(victim.getUniqueId());

        if (damageInfo != null) {
            Player killer = getPlayerFromUUID(damageInfo.getKillerUUID());

            if (killer != null) {
                // Registrar o kill
                StatsManager.registerKill(killer, damageInfo.getWeaponType(), damageInfo.isHeadshot());
            }

            // Remover as informações de dano
            lastDamage.remove(victim.getUniqueId());
        }
    }

    /**
     * Obtém um jogador a partir do UUID.
     */
    private Player getPlayerFromUUID(UUID uuid) {
        return org.bukkit.Bukkit.getPlayer(uuid);
    }

    /**
     * Classe para armazenar informações sobre o último dano causado a uma entidade.
     */
    private static class DamageInfo {
        private final UUID killerUUID;
        private final WeaponType weaponType;
        private final boolean headshot;

        public DamageInfo(UUID killerUUID, WeaponType weaponType, boolean headshot) {
            this.killerUUID = killerUUID;
            this.weaponType = weaponType;
            this.headshot = headshot;
        }

        public UUID getKillerUUID() {
            return killerUUID;
        }

        public WeaponType getWeaponType() {
            return weaponType;
        }

        public boolean isHeadshot() {
            return headshot;
        }
    }
}
