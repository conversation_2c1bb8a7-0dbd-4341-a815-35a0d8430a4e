<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.esg</groupId>
  <artifactId>esgotoserver</artifactId>
  <version>1.0</version>
  <packaging>jar</packaging>

  <name>esgotoserver</name>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
            <annotationProcessorPaths>
                <path>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                    <version>1.18.30</version>
                </path>
            </annotationProcessorPaths>
        </configuration>
      </plugin>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <version>2.3.1</version>
            <configuration>
                <!-- Não definir outputDirectory aqui, deixar o Maven usar o padrão -->
            </configuration>
        </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <!-- Não definir outputFile aqui, deixar o Maven usar o padrão -->
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- Plugin para copiar o arquivo JAR final para a pasta do servidor -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy file="${project.build.directory}/${project.artifactId}-${project.version}.jar"
                      tofile="C:\Users\<USER>\Desktop\Server\plugins\${project.artifactId}.jar"
                      overwrite="true" />
                <echo message="Copiado ${project.artifactId}-${project.version}.jar para a pasta do servidor como ${project.artifactId}.jar" />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
  </build>

  <repositories>
      <repository>
          <id>spigotmc-repo</id>
          <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
      </repository>
      <repository>
          <id>sonatype</id>
          <url>https://oss.sonatype.org/content/groups/public/</url>
      </repository>
      <repository>
          <id>spigot-repo</id>
          <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
      </repository>
      <repository>
          <id>spigotmc-local</id>
          <url>file://${user.home}/.m2/repository</url>
      </repository>
      <repository>
          <id>jitpack.io</id>
          <url>https://jitpack.io</url>
      </repository>
      <repository>
          <id>codemc-repo</id>
          <url>https://repo.codemc.io/repository/maven-public/</url>
          <layout>default</layout>
      </repository>
  </repositories>

    <dependencies>
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>1.8.8-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot</artifactId>
            <version>1.8.8-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.ConnorLinfoot</groupId>
            <artifactId>ActionBarAPI</artifactId>
            <version>5b2d642d3df</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>de.tr7zw</groupId>
            <artifactId>item-nbt-api-plugin</artifactId>
            <version>2.14.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sparkjava</groupId>
            <artifactId>spark-core</artifactId>
            <version>2.9.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>2.0.7</version>
        </dependency>
    </dependencies>
</project>
