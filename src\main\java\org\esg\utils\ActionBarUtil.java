package org.esg.utils;

import net.minecraft.server.v1_8_R3.IChatBaseComponent;
import net.minecraft.server.v1_8_R3.PacketPlayOutChat;
import org.bukkit.craftbukkit.v1_8_R3.entity.CraftPlayer;
import org.bukkit.entity.Player;

/**
 * Utilitário para enviar mensagens para a action bar dos jogadores.
 * Implementação para o Bukkit 1.8.
 */
public class ActionBarUtil {

    /**
     * Envia uma mensagem para a action bar do jogador.
     * 
     * @param player O jogador
     * @param message A mensagem
     */
    public static void sendActionBar(Player player, String message) {
        IChatBaseComponent chatComponent = IChatBaseComponent.ChatSerializer.a("{\"text\":\"" + message + "\"}");
        PacketPlayOutChat packet = new PacketPlayOutChat(chatComponent, (byte) 2);
        ((CraftPlayer) player).getHandle().playerConnection.sendPacket(packet);
    }
}
