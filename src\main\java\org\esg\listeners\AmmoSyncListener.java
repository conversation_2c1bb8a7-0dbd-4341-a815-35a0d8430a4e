package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.esg.utils.AmmoCache;
import org.esg.utils.NBTUtils;
import org.esg.models.Weapon;

/**
 * Listener responsável por sincronizar o cache de munição com o NBT
 * em momentos específicos para evitar perda de dados.
 */
public class AmmoSyncListener implements Listener {
    
    /**
     * Quando o jogador sai do servidor, sincroniza todas as armas com o NBT
     * para garantir que os dados sejam salvos.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Sincronizar todas as armas no inventário
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null) {
                String weaponId = NBTUtils.getWeaponID(item);
                if (weaponId != null) {
                    // Forçar atualização do NBT com os dados do cache
                    AmmoCache.syncWithNBT(player, item, true);
                }
            }
        }
        
        // Limpar o cache para o jogador
        AmmoCache.removePlayer(player);
    }
    
    /**
     * Quando o jogador troca de item na hotbar, sincronizar o item anterior
     * para garantir que os dados sejam salvos.
     */
    @EventHandler
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        int previousSlot = event.getPreviousSlot();
        
        // Sincronizar a arma do slot anterior, se houver
        ItemStack previousItem = player.getInventory().getItem(previousSlot);
        if (previousItem != null) {
            String weaponId = NBTUtils.getWeaponID(previousItem);
            if (weaponId != null) {
                // Atualizar o NBT com os dados do cache
                AmmoCache.syncWithNBT(player, previousItem, false);
            }
        }
        
        // Inicializar o cache para a nova arma, se necessário
        ItemStack newItem = player.getInventory().getItem(event.getNewSlot());
        if (newItem != null) {
            Weapon weapon = NBTUtils.getWeaponFromNBT(newItem, player);
            if (weapon != null) {
                // Garantir que o cache está atualizado para a nova arma
                AmmoCache.getAmmo(player, newItem);
            }
        }
    }
} 