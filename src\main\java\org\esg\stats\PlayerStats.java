package org.esg.stats;

import java.util.UUID;

/**
 * Representa as estatísticas de um jogador.
 */
public class PlayerStats {

    private final UUID playerUUID;
    private final String playerName;

    // Estatísticas básicas
    private int kills;
    private int deaths;
    private int headshots;
    private int shotsFired;
    private int shotsHit;

    // Estatísticas por arma
    private int rifleKills;
    private int shotgunKills;
    private int sniperKills;
    private int smgKills;
    private int pistolKills;

    // Estatísticas de precisão
    private int rifleShots;
    private int shotgunShots;
    private int sniperShots;
    private int smgShots;
    private int pistolShots;

    // Estatísticas de headshots
    private int rifleHeadshots;
    private int shotgunHeadshots;
    private int sniperHeadshots;
    private int smgHeadshots;
    private int pistolHeadshots;

    /**
     * Cria uma nova instância de estatísticas para um jogador.
     *
     * @param playerUUID O UUID do jogador
     * @param playerName O nome do jogador
     */
    public PlayerStats(UUID playerUUID, String playerName) {
        this.playerUUID = playerUUID;
        this.playerName = playerName;
    }

    /**
     * Obtém o UUID do jogador.
     *
     * @return O UUID do jogador
     */
    public UUID getPlayerUUID() {
        return playerUUID;
    }

    /**
     * Obtém o nome do jogador.
     *
     * @return O nome do jogador
     */
    public String getPlayerName() {
        return playerName;
    }

    /**
     * Incrementa o contador de kills.
     */
    public void addKill() {
        kills++;
    }

    /**
     * Define o número de kills.
     *
     * @param kills O número de kills
     */
    public void addKill(int kills) {
        this.kills += kills;
    }

    /**
     * Incrementa o contador de kills para um tipo específico de arma.
     *
     * @param weaponType O tipo de arma
     */
    public void addKill(org.esg.enums.WeaponType weaponType) {
        addKill();

        switch (weaponType) {
            case RIFLE:
                rifleKills++;
                break;
            case SHOTGUN:
                shotgunKills++;
                break;
            case SNIPER:
                sniperKills++;
                break;
            case SMG:
                smgKills++;
                break;
            case PISTOL:
                pistolKills++;
                break;
        }
    }

    /**
     * Incrementa o contador de mortes.
     */
    public void addDeath() {
        deaths++;
    }

    /**
     * Define o número de mortes.
     *
     * @param deaths O número de mortes
     */
    public void addDeath(int deaths) {
        this.deaths += deaths;
    }

    /**
     * Incrementa o contador de headshots.
     */
    public void addHeadshot() {
        headshots++;
    }

    /**
     * Define o número de headshots.
     *
     * @param headshots O número de headshots
     */
    public void addHeadshot(int headshots) {
        this.headshots += headshots;
    }

    /**
     * Incrementa o contador de headshots para um tipo específico de arma.
     *
     * @param weaponType O tipo de arma
     */
    public void addHeadshot(org.esg.enums.WeaponType weaponType) {
        addHeadshot();

        switch (weaponType) {
            case RIFLE:
                rifleHeadshots++;
                break;
            case SHOTGUN:
                shotgunHeadshots++;
                break;
            case SNIPER:
                sniperHeadshots++;
                break;
            case SMG:
                smgHeadshots++;
                break;
            case PISTOL:
                pistolHeadshots++;
                break;
        }
    }

    /**
     * Incrementa o contador de tiros disparados.
     */
    public void addShotFired() {
        shotsFired++;
    }

    /**
     * Define o número de tiros disparados.
     *
     * @param shotsFired O número de tiros disparados
     */
    public void addShotFired(int shotsFired) {
        this.shotsFired += shotsFired;
    }

    /**
     * Incrementa o contador de tiros disparados para um tipo específico de arma.
     *
     * @param weaponType O tipo de arma
     */
    public void addShotFired(org.esg.enums.WeaponType weaponType) {
        addShotFired();

        switch (weaponType) {
            case RIFLE:
                rifleShots++;
                break;
            case SHOTGUN:
                shotgunShots++;
                break;
            case SNIPER:
                sniperShots++;
                break;
            case SMG:
                smgShots++;
                break;
            case PISTOL:
                pistolShots++;
                break;
        }
    }

    /**
     * Incrementa o contador de tiros acertados.
     */
    public void addShotHit() {
        shotsHit++;
    }

    /**
     * Define o número de tiros acertados.
     *
     * @param shotsHit O número de tiros acertados
     */
    public void addShotHit(int shotsHit) {
        this.shotsHit += shotsHit;
    }

    /**
     * Calcula a precisão do jogador (porcentagem de tiros acertados).
     *
     * @return A precisão do jogador (0-100)
     */
    public double getAccuracy() {
        if (shotsFired == 0) {
            return 0.0;
        }

        return (double) shotsHit / shotsFired * 100.0;
    }

    /**
     * Calcula a taxa de headshots do jogador (porcentagem de kills que foram headshots).
     *
     * @return A taxa de headshots do jogador (0-100)
     */
    public double getHeadshotRate() {
        if (kills == 0) {
            return 0.0;
        }

        return (double) headshots / kills * 100.0;
    }

    /**
     * Calcula o K/D ratio do jogador (kills dividido por mortes).
     *
     * @return O K/D ratio do jogador
     */
    public double getKDRatio() {
        if (deaths == 0) {
            return kills; // Evitar divisão por zero
        }

        return (double) kills / deaths;
    }

    // Métodos para estatísticas específicas por arma

    public void addRifleKill(int kills) {
        this.rifleKills += kills;
    }

    public void addShotgunKill(int kills) {
        this.shotgunKills += kills;
    }

    public void addSniperKill(int kills) {
        this.sniperKills += kills;
    }

    public void addSmgKill(int kills) {
        this.smgKills += kills;
    }

    public void addPistolKill(int kills) {
        this.pistolKills += kills;
    }

    public void addRifleHeadshot(int headshots) {
        this.rifleHeadshots += headshots;
    }

    public void addShotgunHeadshot(int headshots) {
        this.shotgunHeadshots += headshots;
    }

    public void addSniperHeadshot(int headshots) {
        this.sniperHeadshots += headshots;
    }

    public void addSmgHeadshot(int headshots) {
        this.smgHeadshots += headshots;
    }

    public void addPistolHeadshot(int headshots) {
        this.pistolHeadshots += headshots;
    }

    public void addRifleShot(int shots) {
        this.rifleShots += shots;
    }

    public void addShotgunShot(int shots) {
        this.shotgunShots += shots;
    }

    public void addSniperShot(int shots) {
        this.sniperShots += shots;
    }

    public void addSmgShot(int shots) {
        this.smgShots += shots;
    }

    public void addPistolShot(int shots) {
        this.pistolShots += shots;
    }

    // Getters para todas as estatísticas

    public int getKills() {
        return kills;
    }

    public int getDeaths() {
        return deaths;
    }

    public int getHeadshots() {
        return headshots;
    }

    public int getShotsFired() {
        return shotsFired;
    }

    public int getShotsHit() {
        return shotsHit;
    }

    public int getRifleKills() {
        return rifleKills;
    }

    public int getShotgunKills() {
        return shotgunKills;
    }

    public int getSniperKills() {
        return sniperKills;
    }

    public int getSmgKills() {
        return smgKills;
    }

    public int getPistolKills() {
        return pistolKills;
    }

    public int getRifleHeadshots() {
        return rifleHeadshots;
    }

    public int getShotgunHeadshots() {
        return shotgunHeadshots;
    }

    public int getSniperHeadshots() {
        return sniperHeadshots;
    }

    public int getSmgHeadshots() {
        return smgHeadshots;
    }

    public int getPistolHeadshots() {
        return pistolHeadshots;
    }

    public int getRifleShots() {
        return rifleShots;
    }

    public int getShotgunShots() {
        return shotgunShots;
    }

    public int getSniperShots() {
        return sniperShots;
    }

    public int getSmgShots() {
        return smgShots;
    }

    public int getPistolShots() {
        return pistolShots;
    }
}
