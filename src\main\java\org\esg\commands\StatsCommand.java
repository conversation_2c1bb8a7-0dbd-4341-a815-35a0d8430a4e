package org.esg.commands;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.esg.stats.PlayerStats;
import org.esg.stats.StatsManager;

import java.util.List;

/**
 * Comando para ver estatísticas de jogadores.
 */
public class StatsCommand implements CommandExecutor {
    
    private static final String USAGE_MESSAGE = ChatColor.RED + "Uso: /stats [jogador]";
    private static final String ONLY_PLAYERS_MESSAGE = ChatColor.RED + "Este comando só pode ser usado por jogadores!";
    private static final String PLAYER_NOT_FOUND_MESSAGE = ChatColor.RED + "Jogador não encontrado: %s";
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length > 0 && args[0].equalsIgnoreCase("top")) {
            showLeaderboard(sender, args);
            return true;
        }
        
        Player target;
        
        if (args.length == 0) {
            // Se não houver argumentos, mostrar as estatísticas do próprio jogador
            if (!(sender instanceof Player)) {
                sender.sendMessage(ONLY_PLAYERS_MESSAGE);
                return true;
            }
            
            target = (Player) sender;
        } else {
            // Se houver um argumento, mostrar as estatísticas do jogador especificado
            target = Bukkit.getPlayer(args[0]);
            
            if (target == null) {
                sender.sendMessage(String.format(PLAYER_NOT_FOUND_MESSAGE, args[0]));
                return true;
            }
        }
        
        // Mostrar as estatísticas do jogador
        List<String> stats = StatsManager.formatPlayerStats(target);
        
        for (String line : stats) {
            sender.sendMessage(line);
        }
        
        return true;
    }
    
    private void showLeaderboard(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Uso: /stats top <kills|kd|headshots|accuracy> [quantidade]");
            return;
        }
        
        String category = args[1].toLowerCase();
        int count = 10; // Padrão: top 10
        
        if (args.length >= 3) {
            try {
                count = Integer.parseInt(args[2]);
                count = Math.max(1, Math.min(count, 20)); // Limitar entre 1 e 20
            } catch (NumberFormatException e) {
                sender.sendMessage(ChatColor.RED + "A quantidade deve ser um número entre 1 e 20.");
                return;
            }
        }
        
        List<String> leaderboard;
        
        switch (category) {
            case "kills":
                leaderboard = StatsManager.formatLeaderboard("Top " + count + " Kills", 
                                                           StatsManager.getTopKills(count),
                                                           stats -> String.valueOf(stats.getKills()));
                break;
            case "kd":
                leaderboard = StatsManager.formatLeaderboard("Top " + count + " K/D Ratio", 
                                                           StatsManager.getTopKDRatio(count),
                                                           stats -> String.format("%.2f", stats.getKDRatio()));
                break;
            case "headshots":
                leaderboard = StatsManager.formatLeaderboard("Top " + count + " Headshots", 
                                                           StatsManager.getTopHeadshots(count),
                                                           stats -> String.valueOf(stats.getHeadshots()));
                break;
            case "accuracy":
                leaderboard = StatsManager.formatLeaderboard("Top " + count + " Precisão", 
                                                           StatsManager.getTopAccuracy(count),
                                                           stats -> String.format("%.1f%%", stats.getAccuracy()));
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Categoria inválida. Use: kills, kd, headshots ou accuracy.");
                return;
        }
        
        for (String line : leaderboard) {
            sender.sendMessage(line);
        }
    }
}
