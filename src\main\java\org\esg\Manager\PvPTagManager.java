package org.esg.Manager;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Main;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Gerencia o sistema de PvP tag.
 */
public class PvPTagManager {
    private static final Map<UUID, Long> taggedPlayers = new HashMap<>();
    private static final int TAG_DURATION_SECONDS = 10;

    /**
     * Adiciona um jogador à tag de PvP.
     *
     * @param player O jogador
     */
    public static void tagPlayer(Player player) {
        taggedPlayers.put(player.getUniqueId(), System.currentTimeMillis() + (TAG_DURATION_SECONDS * 1000));
        player.sendMessage(ChatColor.RED + "§l[PvP Tag] §r§cVocê está em combate! Não deslogue por " + TAG_DURATION_SECONDS + " segundos.");

        // Atualizar o scoreboard do jogador
        ScoreboardManager.updateScoreboard(player);
    }

    /**
     * Remove um jogador da tag de PvP.
     *
     * @param player O jogador
     */
    public static void untagPlayer(Player player) {
        taggedPlayers.remove(player.getUniqueId());
        player.sendMessage(ChatColor.GREEN + "§l[PvP Tag] §r§aVocê não está mais em combate.");

        // Atualizar o scoreboard do jogador
        ScoreboardManager.updateScoreboard(player);
    }

    /**
     * Verifica se um jogador está em tag de PvP.
     *
     * @param player O jogador
     * @return true se o jogador estiver em tag de PvP, false caso contrário
     */
    public static boolean isTagged(Player player) {
        Long endTime = taggedPlayers.get(player.getUniqueId());

        if (endTime == null) {
            return false;
        }

        if (System.currentTimeMillis() > endTime) {
            taggedPlayers.remove(player.getUniqueId());
            return false;
        }

        return true;
    }

    /**
     * Obtém o tempo restante da tag de PvP de um jogador em segundos.
     *
     * @param player O jogador
     * @return O tempo restante em segundos, ou 0 se o jogador não estiver em tag de PvP
     */
    public static int getRemainingSeconds(Player player) {
        Long endTime = taggedPlayers.get(player.getUniqueId());

        if (endTime == null) {
            return 0;
        }

        long remainingMillis = endTime - System.currentTimeMillis();

        if (remainingMillis <= 0) {
            taggedPlayers.remove(player.getUniqueId());
            return 0;
        }

        return (int) (remainingMillis / 1000);
    }

    /**
     * Mata um jogador que deslogou durante o PvP tag.
     *
     * @param player O jogador
     */
    public static void handleCombatLogout(Player player) {
        if (isTagged(player)) {
            player.setHealth(0);
            Bukkit.broadcastMessage(ChatColor.RED + "§l[PvP Tag] §r§c" + player.getName() + " deslogou durante o combate e morreu!");
        }
    }

    /**
     * Inicia o sistema de PvP tag.
     *
     * @param plugin A instância principal do plugin
     */
    public static void initialize(Main plugin) {
        // Agendar verificação periódica das tags de PvP
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (isTagged(player)) {
                        // Atualizar o scoreboard do jogador
                        ScoreboardManager.updateScoreboard(player);

                        // Verificar se a tag expirou
                        if (getRemainingSeconds(player) == 0) {
                            player.sendMessage(ChatColor.GREEN + "§l[PvP Tag] §r§aVocê não está mais em combate.");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 20L, 20L);
    }
}
