# Weapon Configuration File
# This file contains weapon-specific settings including knockback immunity durations

# Knockback Immunity System Configuration
# Controls how long players are immune to knockback after being hit by weapons
knockback-immunity:
  # Default immunity duration in milliseconds
  default-duration: 10000
  
  # Weapon type specific immunity durations
  weapon-types:
    # Automatic weapons (high fire rate) - shorter immunity to prevent spam
    automatic-weapons: 10000    # SMG, RIFLE with high fire rate
    
    # Single-shot weapons - standard immunity
    single-shot-weapons: 500  # SHOTGUN, PISTOL, low fire rate weapons
    
    # Sniper weapons - longer immunity due to high damage
    sniper-weapons: 800       # SNIPER type weapons
  
  # Individual weapon overrides (optional)
  # Uncomment and modify to set specific immunity for individual weapons
  weapon-overrides:
    # "AK-47": 350
    # "UZI": 250
    # "Barrett": 1000
    # "Spas-12": 600
    # "AR-15": 400
    # "M4A1": 300
    # "DMR": 500
    # "Dragunov": 900
    # "3oitão": 450
    # "Lança-Chamas": 400
    # "RPG": 1200

# Weapon Statistics Configuration
# Future expansion for weapon-specific settings
weapons:
  # Global weapon settings
  global:
    # Enable/disable knockback immunity system
    knockback-immunity-enabled: true
    
    # Enable debug logging for knockback system
    debug-knockback: false
    
    # Cleanup interval for expired immunity entries (in ticks)
    cleanup-interval: 100
  
  # Individual weapon configurations
  # This section can be expanded for weapon-specific settings
  individual:
    "AK-47":
      # Weapon-specific knockback immunity (overrides type-based setting)
      knockback-immunity: 350
      
    "UZI":
      knockback-immunity: 250
      
    "Barrett":
      knockback-immunity: 1000
      
    "Spas-12":
      knockback-immunity: 600
      
    "AR-15":
      knockback-immunity: 400
      
    "M4A1":
      knockback-immunity: 300
      
    "DMR":
      knockback-immunity: 500
      
    "Dragunov":
      knockback-immunity: 900
      
    "3oitão":
      knockback-immunity: 450
      
    "Lança-Chamas":
      knockback-immunity: 400
      
    "RPG":
      knockback-immunity: 1200

# Notes:
# - All durations are in milliseconds
# - Lower values = more frequent knockback possible
# - Higher values = longer immunity periods
# - Automatic weapons should have lower values to prevent spam
# - High-damage weapons (snipers) should have higher values
# - Individual weapon settings override weapon type settings
