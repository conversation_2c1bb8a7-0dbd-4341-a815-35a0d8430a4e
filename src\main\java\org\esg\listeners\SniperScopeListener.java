package org.esg.listeners;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerToggleSprintEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;
import org.esg.utils.WeaponUtils;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerArmorStandManipulateEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.RegisteredListener;
import org.bukkit.event.HandlerList;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class SniperScopeListener implements Listener {
    private final Set<UUID> scopedPlayers = new HashSet<>();
    private final Map<UUID, ItemStack> helmetBackup = new HashMap<>();
    private final Map<UUID, BukkitRunnable> helmetTasks = new HashMap<>();

    private void enterScope(Player player) {
        UUID uuid = player.getUniqueId();
        if (!scopedPlayers.contains(uuid)) {
            helmetBackup.put(uuid, player.getInventory().getHelmet());
            player.getInventory().setHelmet(new ItemStack(Material.PUMPKIN));
            player.setSprinting(false);
            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOW, Integer.MAX_VALUE, 2, false, false));
            scopedPlayers.add(uuid);

            // Inicia tarefa para forçar a abóbora no capacete
            BukkitRunnable task = new BukkitRunnable() {
                @Override
                public void run() {
                    if (!scopedPlayers.contains(uuid) || !player.isOnline()) {
                        this.cancel();
                        helmetTasks.remove(uuid);
                        return;
                    }
                    ItemStack helmet = player.getInventory().getHelmet();
                    if (helmet == null || helmet.getType() != Material.PUMPKIN) {
                        player.getInventory().setHelmet(new ItemStack(Material.PUMPKIN));
                    }
                }
            };
            task.runTaskTimer(JavaPlugin.getProvidingPlugin(getClass()), 1L, 2L); // a cada 2 ticks
            helmetTasks.put(uuid, task);
        }
    }

    private void exitScope(Player player) {
        UUID uuid = player.getUniqueId();
        if (scopedPlayers.remove(uuid)) {
            ItemStack oldHelmet = helmetBackup.remove(uuid);
            player.getInventory().setHelmet(oldHelmet);
            player.removePotionEffect(PotionEffectType.SLOW);
            // Cancela a task de proteção
            BukkitRunnable task = helmetTasks.remove(uuid);
            if (task != null) task.cancel();
        }
    }

    @EventHandler
    public void onLeftClick(PlayerInteractEvent event) {
        if (!event.getAction().toString().contains("LEFT_CLICK")) return;

        Player player = event.getPlayer();
        Weapon weapon = WeaponUtils.getWeaponFromItem(player.getInventory().getItemInHand(), player);
        if (weapon == null || weapon.getType() != WeaponType.SNIPER) return;

        UUID uuid = player.getUniqueId();
        if (!scopedPlayers.contains(uuid)) {
            enterScope(player);
        } else {
            exitScope(player);
        }
    }

    @EventHandler
    public void onItemSwitch(PlayerItemHeldEvent event) {
        exitScope(event.getPlayer());
    }

    @EventHandler
    public void onDrop(PlayerDropItemEvent event) {
        exitScope(event.getPlayer());
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        exitScope(event.getPlayer());
    }

    @EventHandler
    public void onDeath(PlayerDeathEvent event) {
        exitScope(event.getEntity());
    }

    @EventHandler
    public void onScopeSprint(PlayerToggleSprintEvent event) {
        Player player = event.getPlayer();
        if (scopedPlayers.contains(player.getUniqueId()) && event.isSprinting()) {
            event.setCancelled(true);
            player.setSprinting(false);
        }
    }

    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onHelmetClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        UUID uuid = player.getUniqueId();
        if (!scopedPlayers.contains(uuid)) return;

        // Cancela qualquer interação com o slot 39 (capacete)
        if (event.getSlot() == 39 || event.getRawSlot() == 39) {
            event.setCancelled(true);
        }
        // Cancela qualquer tentativa de trocar por número do teclado
        if (event.getClick() == ClickType.NUMBER_KEY && event.getHotbarButton() >= 0) {
            event.setCancelled(true);
        }
        // Cancela shift-click que tente mover a abóbora para fora do slot de capacete
        if (event.getCurrentItem() != null && event.getCurrentItem().getType() == Material.PUMPKIN && event.getSlot() == 39) {
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onHelmetDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        UUID uuid = player.getUniqueId();
        if (!scopedPlayers.contains(uuid)) return;

        // Se o drag envolve o slot 39 (capacete), cancela
        if (event.getRawSlots().contains(39)) {
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryOpen(InventoryOpenEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player player = (Player) event.getPlayer();
        UUID uuid = player.getUniqueId();
        if (!scopedPlayers.contains(uuid)) return;

        // Garante que o slot 39 está com abóbora ao abrir o inventário
        ItemStack helmet = player.getInventory().getHelmet();
        if (helmet == null || helmet.getType() != Material.PUMPKIN) {
            player.getInventory().setHelmet(new ItemStack(Material.PUMPKIN));
        }
    }

    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onArmorStandManipulate(PlayerArmorStandManipulateEvent event) {
        Player player = event.getPlayer();
        if (scopedPlayers.contains(player.getUniqueId()) && event.getArmorStandItem().getType() == Material.PUMPKIN) {
            event.setCancelled(true);
        }
    }

    // Permite checar se o jogador está scoped de fora da classe
    public static boolean isScoped(Player player) {
        return player != null && SniperScopeListenerHolder.INSTANCE.scopedPlayers.contains(player.getUniqueId());
    }

    // Holder para acesso estático ao set de scopedPlayers
    private static class SniperScopeListenerHolder {
        private static final SniperScopeListener INSTANCE = getInstance();
        private static SniperScopeListener getInstance() {
            Plugin plugin = JavaPlugin.getProvidingPlugin(SniperScopeListener.class);
            for (RegisteredListener rl : HandlerList.getRegisteredListeners(plugin)) {
                Listener listener = rl.getListener();
                if (listener instanceof SniperScopeListener) {
                    return (SniperScopeListener) listener;
                }
            }
            throw new IllegalStateException("SniperScopeListener não registrado!");
        }
    }
} 