package org.esg.listeners;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerAnimationType;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.inventory.ItemStack;
import org.esg.models.Weapon;
import org.esg.utils.MessageHandler;
import org.esg.utils.NBTUtils;
import org.esg.utils.WeaponUtils;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Main;
import org.bukkit.event.Event;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static net.minecraft.server.v1_8_R3.MinecraftServer.LOGGER;

public class ShootListener implements Listener {
    private static final long CLICK_COOLDOWN_MS = 150;
    private static final Map<UUID, Boolean> isFiring = Weapon.getIsFiring();
    private static final Map<UUID, Long> lastClickTimes = Weapon.getLastClickTimes();
    private static final Map<UUID, Weapon> activeWeapons = new HashMap<>();

    // Mapa para rastrear quando o jogador começou a atirar continuamente
    private static final Map<UUID, Long> continuousFiringStartTime = new HashMap<>();

    // Tempo mínimo de disparo contínuo para aplicar cooldown mais rigoroso (em ms)
    private static final long CONTINUOUS_FIRING_THRESHOLD_MS = 1000; // 1 segundo

    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerInteractLowest(PlayerInteractEvent event) {
        // Cancelar animação de uso da enxada o mais cedo possível
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) return;

        Player player = event.getPlayer();
        ItemStack itemInHand = player.getInventory().getItemInHand();

        if (itemInHand != null && (itemInHand.getType() == Material.WOOD_HOE ||
                                 itemInHand.getType() == Material.STONE_HOE ||
                                 itemInHand.getType() == Material.IRON_HOE ||
                                 itemInHand.getType() == Material.GOLD_HOE ||
                                 itemInHand.getType() == Material.DIAMOND_HOE)) {

            // Verificar se é uma arma
            Weapon weapon = WeaponUtils.getWeaponFromItem(itemInHand, player);
            if (weapon != null) {
                // Cancelar completamente o evento no estágio LOWEST
                event.setCancelled(true);
                event.setUseItemInHand(Event.Result.DENY);
                event.setUseInteractedBlock(Event.Result.DENY);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        // Verificar se é clique direito
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) return;

        Player player = event.getPlayer();
        UUID playerUUID = player.getUniqueId();
        ItemStack itemInHand = player.getInventory().getItemInHand();
        Weapon weapon = WeaponUtils.getWeaponFromItem(itemInHand, player);
        if (weapon == null) return;

        // Cancelar completamente o evento para evitar animação padrão
        event.setCancelled(true);
        event.setUseItemInHand(Event.Result.DENY);
        event.setUseInteractedBlock(Event.Result.DENY);

        // Verificar debounce
        long currentTime = System.currentTimeMillis();
        Long lastClick = lastClickTimes.get(playerUUID);
        if (lastClick != null && (currentTime - lastClick) < CLICK_COOLDOWN_MS) return;
        lastClickTimes.put(playerUUID, currentTime);

        // Recarga automática se sem munição ou se o jogador está tentando recarregar
        // Verificar se é um clique direito com botão de shift pressionado (tentativa de recarga manual)
        boolean isManualReload = player.isSneaking();

        if ((weapon.getCurrentAmmo() <= 0 && !weapon.isReloading(player)) ||
            (isManualReload && weapon.canReload(player))) {
            // Cancelar qualquer estado de disparo atual
            isFiring.put(playerUUID, false);
            // Limpar o tempo de início do disparo contínuo
            continuousFiringStartTime.remove(playerUUID);

            if (isManualReload) {
                player.sendMessage("§eRecarregando manualmente...");
            } else {
                player.sendMessage("§eRecarregando automaticamente...");
            }

            weapon.reload(player);
            return;
        }

        // Iniciar disparo (canShoot já verifica o delay de quickswap)
        if (weapon.canShoot(player) && !isFiring.getOrDefault(playerUUID, false)) {
            isFiring.put(playerUUID, true);

            // Registrar o início do disparo contínuo
            continuousFiringStartTime.put(playerUUID, currentTime);

            weapon.startFiring(player);

            // Monitorar parada do disparo
            long maxIdleTimeMs = (long) (1000 / weapon.getFireRate());
            new BukkitRunnable() {
                @Override
                public void run() {
                    long currentTime = System.currentTimeMillis();
                    Long lastClick = lastClickTimes.get(playerUUID);
                    if (lastClick == null || (currentTime - lastClick) > maxIdleTimeMs || weapon.isReloading(player)) {
                        isFiring.put(playerUUID, false);
                        // Limpar o tempo de início do disparo contínuo quando parar de atirar
                        continuousFiringStartTime.remove(playerUUID);
                        cancel();
                    }
                }
            }.runTaskTimer(Main.getPlugin(), 1L, 1L);
        }
    }

    // Cancelar qualquer animação que possa ocorrer
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerAnimation(PlayerAnimationEvent event) {
        Player player = event.getPlayer();
        ItemStack itemInHand = player.getInventory().getItemInHand();
        Weapon weapon = WeaponUtils.getWeaponFromItem(itemInHand, player);

        if (weapon != null) {
            // Cancelar qualquer animação com arma
            event.setCancelled(true);
        }
    }

    // Cancelar interações enquanto segura uma arma
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        Weapon weapon = WeaponUtils.getWeaponFromItem(event.getPlayer().getInventory().getItemInHand(), event.getPlayer());
        if (weapon != null) {
            event.setCancelled(true);
        }
    }

    // Cancelar colocação de blocos com arma
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPlace(BlockPlaceEvent event) {
        Weapon weapon = WeaponUtils.getWeaponFromItem(event.getPlayer().getInventory().getItemInHand(), event.getPlayer());
        if (weapon != null) {
            event.setCancelled(true);
        }
    }

    // Cancelar quebra de blocos com arma
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(BlockBreakEvent event) {
        Weapon weapon = WeaponUtils.getWeaponFromItem(event.getPlayer().getInventory().getItemInHand(), event.getPlayer());
        if (weapon != null) {
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        UUID playerUUID = player.getUniqueId();

        // Parar de atirar ao trocar de item
        isFiring.put(playerUUID, false);
        // Limpar o tempo de início do disparo contínuo
        continuousFiringStartTime.remove(playerUUID);

        // Registrar que o jogador trocou de arma para aplicar cooldown
        Weapon.registerWeaponSwitch(player);

        // Salvar estado da arma anterior
        ItemStack previousItem = player.getInventory().getItem(event.getPreviousSlot());
        if (previousItem != null) {
            Weapon prevWeapon = WeaponUtils.getWeaponFromItem(previousItem, player);
            if (prevWeapon != null) {
                // Cancelar recarga em andamento
                if (prevWeapon.isReloading(player)) {
                    prevWeapon.cancelReload(player);
                }

                // Garantir que a arma pare de atirar
                prevWeapon.stopFiring(player);

                // Atualizar estado da arma no inventário
                WeaponUtils.updateWeaponInSlot(player, event.getPreviousSlot(), prevWeapon);
            }
        }

        // Sistema de timing natural da arma já é aplicado automaticamente
        // através do registerWeaponSwitch() que calcula o delay baseado na fireRate
    }
}