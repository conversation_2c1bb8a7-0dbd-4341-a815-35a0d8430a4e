package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;

public class UZI extends Weapon {

    public UZI() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis
        super("UZI", WeaponType.SMG, AmmoType._9MM, 2.5, 40, 0.6, 15, 70, 25, 25, 2, 1);

        // A UZI tem:
        // - Dano muito menor (0.05 vs 0.1 da AK47)
        // - Alcance menor (40 vs 80 da AK47)
        // - Pre<PERSON><PERSON> baixa (0.4 vs 0.7 da AK47) - Isso causa um espalhamento maior
        // - Velocidade de tiro muito alta (15 vs 10 da AK47)
        // - Velocidade de projétil menor (70 vs 90 da AK47)
        // - 1 projétil por tiro
        // - SMG de alta cadência, ideal para combate próximo
        // - <PERSON><PERSON> de recarga menor (1.5 vs 2 da AK47)
    }
}
