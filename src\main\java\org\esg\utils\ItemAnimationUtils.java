package org.esg.utils;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.esg.Main;
import org.esg.models.Weapon;

/**
 * Classe utilitária para controlar animações de itens,
 * especialmente para prevenir a animação padrão de enxada quando usada como arma.
 */
public class ItemAnimationUtils implements Listener {

    /**
     * Registra o listener para este utilitário.
     * Deve ser chamado no onEnable do plugin principal.
     */
    public static void registerListeners() {
        Plugin plugin = Main.getPlugin();
        plugin.getServer().getPluginManager().registerEvents(new ItemAnimationUtils(), plugin);
    }

    /**
     * Captura eventos de interação com prioridade máxima para cancelar
     * a animação padrão de uso da enxada quando for uma arma.
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        // Verificar se é clique direito
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Player player = event.getPlayer();
        ItemStack itemInHand = player.getInventory().getItemInHand();
        
        // Verificar se o item é uma arma
        Weapon weapon = WeaponUtils.getWeaponFromItem(itemInHand, player);
        
        if (weapon != null) {
            // Cancelar o evento para prevenir a animação de uso da enxada
            event.setCancelled(true);
            
            // Usar setUseInteractedBlock e setUseItemInHand com DENY
            event.setUseInteractedBlock(org.bukkit.event.Event.Result.DENY);
            event.setUseItemInHand(org.bukkit.event.Event.Result.DENY);
        }
    }
} 