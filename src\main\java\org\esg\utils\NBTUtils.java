package org.esg.utils;

import de.tr7zw.nbtapi.NBTItem;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.esg.models.Weapon;
import org.esg.weapons.WeaponFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Logger;

/**
 * Classe utilitária para manipular dados NBT em itens associados a armas.
 */
public final class NBTUtils {
    private static final Logger LOGGER = Logger.getLogger(NBTUtils.class.getName());
    private static final Map<String, CachedFiringState> cachedIsFiring = new HashMap<>();

    private static class CachedFiringState {
        boolean isFiring;
        long timestamp;

        CachedFiringState(boolean isFiring, long timestamp) {
            this.isFiring = isFiring;
            this.timestamp = timestamp;
        }
    }

    private NBTUtils() {
        // Construtor privado para evitar instanciação
    }

    /**
     * Aplica dados NBT de arma a um item.
     */
    public static ItemStack applyWeaponNBT(ItemStack item, Weapon weapon, Player player) {
        if (item == null || player == null) return null;

        // Comentado para reduzir log spam
        // LOGGER.info("Aplicando NBT para arma " + weapon.getName() + " com material " + item.getType().name());

        NBTItem nbtItem = new NBTItem(item);
        if (!nbtItem.hasTag("weapon_id")) {
            String uuid = UUID.randomUUID().toString();
            nbtItem.setString("weapon_id", uuid);
            // LOGGER.info("Novo weapon_id criado: " + uuid);
        } else {
            // LOGGER.info("Usando weapon_id existente: " + nbtItem.getString("weapon_id"));
        }

        setWeaponAttributes(nbtItem, weapon);
        return nbtItem.getItem();
    }

    /**
     * Atualiza apenas a munição da arma no item sem causar uma animação de substituição.
     * Aplicado diretamente ao item no inventário.
     *
     * @param item O item a ser atualizado
     * @param currentAmmo Nova quantidade de munição
     * @return O item atualizado (referência original, modificada)
     */
    public static ItemStack updateAmmoOnly(ItemStack item, int currentAmmo) {
        if (item == null || !item.hasItemMeta()) return item;

        NBTItem nbtItem = new NBTItem(item, true); // true = aplicar alterações diretamente ao item original
        if (!nbtItem.hasTag("weapon_id")) return item;

        nbtItem.setInteger("current_ammo", currentAmmo);
        nbtItem.setLong("last_updated", System.currentTimeMillis());

        // Não é necessário retornar o item quando usamos o modo direto,
        // mas retornamos para manter a consistência da API
        return item;
    }

    /**
     * Define os atributos da arma no item NBT.
     */
    private static void setWeaponAttributes(NBTItem nbtItem, Weapon weapon) {
        // Comentado para reduzir log spam
        // LOGGER.info("Definindo atributos para " + weapon.getName());

        nbtItem.setString("weapon_name", weapon.getName());
        nbtItem.setString("weapon_type", weapon.getType().name());
        nbtItem.setDouble("damage", weapon.getDamage());
        nbtItem.setDouble("range", weapon.getRange());
        nbtItem.setDouble("accuracy", weapon.getAccuracy());
        nbtItem.setDouble("fire_rate", weapon.getFireRate());
        nbtItem.setDouble("projectile_speed", weapon.getProjectileSpeed());
        nbtItem.setInteger("max_ammo", weapon.getMaxAmmo());
        nbtItem.setInteger("current_ammo", weapon.getCurrentAmmo());
        nbtItem.setInteger("reload_time", weapon.getReloadTime());
        nbtItem.setInteger("projectile_count", weapon.getProjectileCount());
        nbtItem.setBoolean("is_reloading", weapon.isReloading());
        nbtItem.setLong("last_updated", System.currentTimeMillis());

        // Comentado para reduzir log spam
        // LOGGER.info("Atributos NBT definidos com sucesso para " + weapon.getName());
    }

    /**
     * Obtém uma arma a partir dos dados NBT de um item.
     */
    public static Weapon getWeaponFromNBT(ItemStack item, Player player) {
        if (item == null) {
            // Removido log de warning para reduzir spam no console
            return null;
        }

        if (!item.hasItemMeta()) {
            // Removido log de warning para reduzir spam no console
            return null;
        }

        if (player == null) {
            // Removido log de warning para reduzir spam no console
            return null;
        }

        NBTItem nbtItem = new NBTItem(item);

        if (!nbtItem.hasTag("weapon_id")) {
            // Removido log de warning para reduzir spam no console
            return null;
        }

        String weaponId = nbtItem.getString("weapon_id");
        String weaponName = nbtItem.getString("weapon_name");
        int currentAmmo = nbtItem.getInteger("current_ammo");
        boolean isReloading = nbtItem.getBoolean("is_reloading");

        try {
            Weapon weapon = WeaponFactory.createWeapon(weaponName);
            weapon.setCurrentAmmo(currentAmmo);
            weapon.setReloading(isReloading);
            return weapon;
        } catch (IllegalArgumentException e) {
            // Log mantido apenas para erros críticos
            LOGGER.warning("Falha ao criar arma a partir de NBT: " + e.getMessage());
            return null;
        }
    }

    /**
     * Obtém o ID da arma a partir de um item.
     */
    public static String getWeaponID(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return null;

        NBTItem nbtItem = new NBTItem(item);
        String weaponId = nbtItem.hasTag("weapon_id") ? nbtItem.getString("weapon_id") : null;

        if (weaponId == null) {
            // Removido log de warning para reduzir spam no console
        }

        return weaponId;
    }
}