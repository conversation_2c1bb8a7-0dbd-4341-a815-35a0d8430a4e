import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Exemplo de como usar os novos métodos da ArmorAPI.
 * Este plugin demonstra como criar e equipar peças individuais de armadura.
 */
public class ArmorAPIUsageExample extends JavaPlugin implements Listener {

    private boolean armorAPIAvailable = false;
    private Class<?> armorAPIClass;
    
    @Override
    public void onEnable() {
        // Verificar se o plugin de armaduras está disponível
        if (checkArmorPlugin()) {
            getLogger().info("Plugin de armaduras encontrado! Integrando...");
        } else {
            getLogger().warning("Plugin de armaduras não encontrado! Funcionalidades de armaduras estarão desativadas.");
        }
        
        // Registrar eventos e comandos
        getServer().getPluginManager().registerEvents(this, this);
        getCommand("armorshop").setExecutor(new ArmorShopCommand(this));
    }

    /**
     * Verifica se o plugin de armaduras está disponível e configura a API.
     */
    private boolean checkArmorPlugin() {
        Plugin armorPlugin = Bukkit.getPluginManager().getPlugin("esgotoserver");
        if (armorPlugin == null || !armorPlugin.isEnabled()) {
            return false;
        }

        try {
            // Obter a classe da API através de reflexão
            Class<?> mainClass = Class.forName("org.esg.Main");
            Method getAPIMethod = mainClass.getMethod("getArmorAPI");
            armorAPIClass = (Class<?>) getAPIMethod.invoke(null);

            // Se chegou até aqui, a API está disponível
            armorAPIAvailable = true;
            return true;
        } catch (Exception e) {
            getLogger().warning("Erro ao acessar a API de armaduras: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Abre um menu de loja de armaduras para o jogador.
     */
    public void openArmorShop(Player player) {
        if (!armorAPIAvailable) {
            player.sendMessage("§cLoja de armaduras indisponível no momento!");
            return;
        }
        
        Inventory shop = Bukkit.createInventory(null, 27, "§8Loja de Armaduras");
        
        try {
            // Adicionar capacetes
            Method getLeatherHelmetItemStackMethod = armorAPIClass.getMethod("getLeatherHelmetItemStack");
            Method getKevlarHelmetItemStackMethod = armorAPIClass.getMethod("getKevlarHelmetItemStack");
            
            ItemStack leatherHelmet = (ItemStack) getLeatherHelmetItemStackMethod.invoke(null);
            ItemStack kevlarHelmet = (ItemStack) getKevlarHelmetItemStackMethod.invoke(null);
            
            shop.setItem(0, leatherHelmet);
            shop.setItem(1, kevlarHelmet);
            
            // Adicionar peitorais
            Method getLeatherChestplateItemStackMethod = armorAPIClass.getMethod("getLeatherChestplateItemStack");
            Method getKevlarChestplateItemStackMethod = armorAPIClass.getMethod("getKevlarChestplateItemStack");
            
            ItemStack leatherChestplate = (ItemStack) getLeatherChestplateItemStackMethod.invoke(null);
            ItemStack kevlarChestplate = (ItemStack) getKevlarChestplateItemStackMethod.invoke(null);
            
            shop.setItem(9, leatherChestplate);
            shop.setItem(10, kevlarChestplate);
            
            // Adicionar calças
            Method getLeatherLeggingsItemStackMethod = armorAPIClass.getMethod("getLeatherLeggingsItemStack");
            Method getKevlarLeggingsItemStackMethod = armorAPIClass.getMethod("getKevlarLeggingsItemStack");
            
            ItemStack leatherLeggings = (ItemStack) getLeatherLeggingsItemStackMethod.invoke(null);
            ItemStack kevlarLeggings = (ItemStack) getKevlarLeggingsItemStackMethod.invoke(null);
            
            shop.setItem(18, leatherLeggings);
            shop.setItem(19, kevlarLeggings);
            
            // Adicionar botas
            Method getLeatherBootsItemStackMethod = armorAPIClass.getMethod("getLeatherBootsItemStack");
            Method getKevlarBootsItemStackMethod = armorAPIClass.getMethod("getKevlarBootsItemStack");
            
            ItemStack leatherBoots = (ItemStack) getLeatherBootsItemStackMethod.invoke(null);
            ItemStack kevlarBoots = (ItemStack) getKevlarBootsItemStackMethod.invoke(null);
            
            shop.setItem(27, leatherBoots);
            shop.setItem(28, kevlarBoots);
            
            player.openInventory(shop);
        } catch (Exception e) {
            getLogger().warning("Erro ao criar loja de armaduras: " + e.getMessage());
            player.sendMessage("§cErro ao abrir a loja de armaduras!");
        }
    }
    
    /**
     * Manipula o evento de clique no inventário da loja de armaduras.
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!armorAPIAvailable || event.getView().getTitle().equals("§8Loja de Armaduras")) {
            return;
        }
        
        event.setCancelled(true);
        
        if (event.getCurrentItem() == null || event.getCurrentItem().getType() == Material.AIR) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        ItemStack clickedItem = event.getCurrentItem();
        
        try {
            // Verificar se o item é uma armadura
            Method createArmorPieceMethod = armorAPIClass.getMethod("createArmorPiece", String.class, String.class);
            
            // Determinar o tipo e a peça com base no slot clicado
            String armorType = event.getSlot() % 9 == 0 ? "LEATHER" : "KEVLAR";
            String armorPiece;
            
            if (event.getSlot() < 9) {
                armorPiece = "HELMET";
            } else if (event.getSlot() < 18) {
                armorPiece = "CHESTPLATE";
            } else if (event.getSlot() < 27) {
                armorPiece = "LEGGINGS";
            } else {
                armorPiece = "BOOTS";
            }
            
            // Equipar a peça de armadura no jogador
            Method equipArmorPieceMethod = armorAPIClass.getMethod("equipArmorPiece", Player.class, String.class, String.class);
            boolean success = (boolean) equipArmorPieceMethod.invoke(null, player, armorType, armorPiece);
            
            if (success) {
                player.sendMessage("§aVocê comprou e equipou " + clickedItem.getItemMeta().getDisplayName() + "§a!");
                player.closeInventory();
            } else {
                player.sendMessage("§cErro ao equipar a armadura!");
            }
        } catch (Exception e) {
            getLogger().warning("Erro ao processar compra de armadura: " + e.getMessage());
            player.sendMessage("§cErro ao processar sua compra!");
        }
    }
    
    /**
     * Comando para abrir a loja de armaduras.
     */
    public class ArmorShopCommand implements CommandExecutor {
        private final ArmorAPIUsageExample plugin;
        
        public ArmorShopCommand(ArmorAPIUsageExample plugin) {
            this.plugin = plugin;
        }
        
        @Override
        public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
                return true;
            }
            
            Player player = (Player) sender;
            plugin.openArmorShop(player);
            return true;
        }
    }
}
