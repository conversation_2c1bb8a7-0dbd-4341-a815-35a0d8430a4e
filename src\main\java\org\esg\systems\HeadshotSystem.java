package org.esg.systems;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.esg.enums.WeaponType;

/**
 * Sistema que gerencia headshots (acertos na cabeça).
 * Permite dano crítico e efeitos especiais para acertos na cabeça.
 */
public class HeadshotSystem {

    // Multiplicadores de dano para headshots por tipo de arma
    public static final double SNIPER_HEADSHOT_MULTIPLIER = 3.0;  // 3x dano para snipers
    public static final double RIFLE_HEADSHOT_MULTIPLIER = 2.5;   // 2.5x dano para rifles
    public static final double SMG_HEADSHOT_MULTIPLIER = 2.0;     // 2x dano para SMGs
    public static final double PISTOL_HEADSHOT_MULTIPLIER = 2.2;  // 2.2x dano para pistolas
    public static final double SHOTGUN_HEADSHOT_MULTIPLIER = 1.8; // 1.8x dano para shotguns

    // Altura da cabeça em relação ao centro da entidade
    private static final double HEAD_HEIGHT_OFFSET = 0.6;

    // Raio da cabeça para detecção de headshots (aumentado para tornar mais fácil)
    private static final double HEAD_RADIUS = 0.45;

    // Chance adicional de headshot para compensar imprecisões de cálculo
    private static final double HEADSHOT_CHANCE = 0.2; // 20% de chance adicional

    /**
     * Verifica se um tiro acertou a cabeça de uma entidade.
     * Agora verifica também o tipo de arma - apenas snipers podem causar headshots.
     *
     * @param projectileLocation A localização do projétil
     * @param target A entidade alvo
     * @param weapon A arma usada para o tiro
     * @return true se foi um headshot, false caso contrário
     */
    public static boolean isHeadshot(Location projectileLocation, LivingEntity target, org.esg.models.Weapon weapon) {
        // Verificar se a arma é do tipo sniper - apenas snipers podem causar headshots
        if (weapon.getType() != WeaponType.SNIPER) {
            return false;
        }

        try {
            // Obter a localização da cabeça da entidade
            Location headLocation = target.getLocation().clone();
            headLocation.setY(headLocation.getY() + target.getEyeHeight() - 0.2); // Ajuste para o centro da cabeça

            // Calcular a distância entre o projétil e a cabeça
            double distance = projectileLocation.distance(headLocation);

            // Verificar a diferença de altura - importante para headshots
            double heightDifference = Math.abs(projectileLocation.getY() - headLocation.getY());

            // Verificações mais equilibradas para headshots

            // Headshot direto - projétil próximo do centro da cabeça (mais fácil)
            if (distance <= HEAD_RADIUS && heightDifference <= 0.3) {
                return true;
            }

            // Headshot próximo - projétil próximo da cabeça (menos restrito)
            if (distance <= HEAD_RADIUS * 1.5 && heightDifference <= 0.4) {
                // Verificar se a altura está na região superior da entidade
                double entityCenterY = target.getLocation().getY() + (target.getEyeHeight() / 3);
                if (projectileLocation.getY() > entityCenterY) {
                    // 70% de chance para tiros na região da cabeça (aumentado de 50%)
                    return Math.random() < 0.7;
                }
            }

            // Chance moderada de headshot para tiros próximos da cabeça (menos restrito)
            if (distance <= HEAD_RADIUS * 2.0 && heightDifference <= 0.5) {
                // A chance diminui com a distância
                double chanceMultiplier = 1.0 - (distance / (HEAD_RADIUS * 2.0));
                return Math.random() < HEADSHOT_CHANCE * chanceMultiplier * 1.5; // Chance base com multiplicador
            }

            // Chance mínima para tiros na região geral da cabeça (aumentada)
            if (projectileLocation.getY() > target.getLocation().getY() + target.getEyeHeight() * 0.7) {
                return Math.random() < 0.1; // 10% de chance para tiros na altura certa (aumentado de 5%)
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Método de compatibilidade para código existente.
     * Sempre retorna false para manter a compatibilidade, mas não deve ser usado.
     *
     * @param projectileLocation A localização do projétil
     * @param target A entidade alvo
     * @return false (headshots agora requerem informação da arma)
     * @deprecated Use isHeadshot(Location, LivingEntity, Weapon) em vez disso
     */
    @Deprecated
    public static boolean isHeadshot(Location projectileLocation, LivingEntity target) {
        // Este método é mantido apenas para compatibilidade
        // Sempre retorna false para evitar headshots sem informação da arma
        return false;
    }

    /**
     * Calcula o dano de headshot com base no tipo de arma.
     *
     * @param baseDamage O dano base
     * @param weaponType O tipo de arma
     * @return O dano aumentado para headshot
     * @deprecated Use calculateHeadshotDamage(double baseDamage, Weapon weapon) em vez disso
     */
    @Deprecated
    public static double calculateHeadshotDamage(double baseDamage, WeaponType weaponType) {
        double multiplier;

        switch (weaponType) {
            case SNIPER:
                multiplier = SNIPER_HEADSHOT_MULTIPLIER;
                break;
            case RIFLE:
                multiplier = RIFLE_HEADSHOT_MULTIPLIER;
                break;
            case SMG:
                multiplier = SMG_HEADSHOT_MULTIPLIER;
                break;
            case PISTOL:
                multiplier = PISTOL_HEADSHOT_MULTIPLIER;
                break;
            case SHOTGUN:
                multiplier = SHOTGUN_HEADSHOT_MULTIPLIER;
                break;
            default:
                multiplier = 2.0; // Valor padrão
        }

        return baseDamage * multiplier;
    }

    /**
     * Calcula o dano de headshot com base no multiplicador específico da arma.
     * Para snipers, o multiplicador varia com a distância.
     * Se a arma não tiver um multiplicador específico, usa o padrão do tipo de arma.
     *
     * @param baseDamage O dano base
     * @param weapon A arma usada
     * @param distanceTraveled A distância percorrida pelo projétil (opcional)
     * @return O dano aumentado para headshot
     */
    public static double calculateHeadshotDamage(double baseDamage, org.esg.models.Weapon weapon) {
        return calculateHeadshotDamage(baseDamage, weapon, -1);
    }

    /**
     * Calcula o dano de headshot com base no multiplicador específico da arma e na distância.
     * Para snipers, o multiplicador varia com a distância:
     * - Curta distância: multiplicador reduzido (70% do normal)
     * - Média distância: multiplicador normal
     * - Longa distância: multiplicador aumentado (até 150% do normal)
     *
     * @param baseDamage O dano base
     * @param weapon A arma usada
     * @param distanceTraveled A distância percorrida pelo projétil
     * @return O dano aumentado para headshot
     */
    public static double calculateHeadshotDamage(double baseDamage, org.esg.models.Weapon weapon, double distanceTraveled) {
        double baseMultiplier = weapon.getHeadshotMultiplier();

        // Para snipers, ajustar o multiplicador com base na distância
        if (weapon.getType() == WeaponType.SNIPER && distanceTraveled > 0) {
            double range = weapon.getRange();
            double distanceRatio = Math.min(1.0, distanceTraveled / range);

            // Multiplicador de distância: 0.8x a curta distância, 1.0x a média distância, 1.8x a longa distância
            double distanceMultiplier = 0.8 + (distanceRatio * 1.0);

            // Limitar o multiplicador entre 0.8 e 1.8
            distanceMultiplier = Math.max(0.8, Math.min(1.8, distanceMultiplier));

            // Aplicar o multiplicador de distância ao multiplicador base
            return baseDamage * (baseMultiplier * distanceMultiplier);
        }

        // Para outras armas, usar o multiplicador base
        return baseDamage * baseMultiplier;
    }

    /**
     * Reproduz efeitos sonoros de headshot para o jogador.
     *
     * @param player O jogador que acertou o headshot
     */
    public static void playHeadshotSound(Player player) {
        try {
            // Removido: não tocar mais SUCCESSFUL_HIT, ANVIL_LAND, EXPLODE, LEVEL_UP
            // player.playSound(player.getLocation(), org.bukkit.Sound.SUCCESSFUL_HIT, 1.0f, 1.5f);
            // player.playSound(player.getLocation(), org.bukkit.Sound.ANVIL_LAND, 0.7f, 2.0f);
            // player.playSound(player.getLocation(), org.bukkit.Sound.EXPLODE, 0.3f, 2.0f);
            // player.playSound(player.getLocation(), org.bukkit.Sound.LEVEL_UP, 0.5f, 1.2f);
        } catch (Exception e) {
            // Ignorar erros de som para evitar crashes
        }
    }

    // Método notifyHeadshot removido, pois agora usamos apenas o HUD para notificações
}
