package org.esg.systems;

import org.bukkit.Material;
import org.bukkit.block.Block;
import org.esg.enums.WeaponType;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Sistema que gerencia a penetração de projéteis através de materiais.
 * Permite que projéteis atravessem certos materiais com dano reduzido.
 */
public class PenetrationSystem {

    // Materiais que podem ser penetrados por projéteis
    private static final Set<Material> PENETRABLE_MATERIALS = new HashSet<>(Arrays.asList(
            Material.LEAVES, Material.LEAVES_2,     // Folhas
            Material.THIN_GLASS, Material.GLASS,    // Vidro
            Material.STAINED_GLASS, Material.STAINED_GLASS_PANE,
            Material.FENCE, Material.FENCE_GATE,    // Cercas de madeira
            Material.IRON_FENCE,                    // Cerca de ferro
            Material.WEB,                           // Teia de aranha
            Material.VINE                           // Vinhas
    ));

    // Fator de redução de dano por tipo de arma (quanto maior, menor a redução)
    private static final double SNIPER_PENETRATION_FACTOR = 0.8;  // 20% de redução por bloco
    private static final double RIFLE_PENETRATION_FACTOR = 0.6;   // 40% de redução por bloco
    private static final double SMG_PENETRATION_FACTOR = 0.4;     // 60% de redução por bloco
    private static final double PISTOL_PENETRATION_FACTOR = 0.3;  // 70% de redução por bloco
    private static final double SHOTGUN_PENETRATION_FACTOR = 0.5; // 50% de redução por bloco

    /**
     * Verifica se um bloco pode ser penetrado por projéteis.
     * 
     * @param block O bloco a ser verificado
     * @return true se o bloco pode ser penetrado, false caso contrário
     */
    public static boolean canPenetrate(Block block) {
        return block != null && PENETRABLE_MATERIALS.contains(block.getType());
    }

    /**
     * Calcula o dano após penetrar um bloco, com base no tipo de arma.
     * 
     * @param damage O dano original
     * @param weaponType O tipo de arma
     * @return O dano reduzido após a penetração
     */
    public static double calculatePenetrationDamage(double damage, WeaponType weaponType) {
        double penetrationFactor;
        
        switch (weaponType) {
            case SNIPER:
                penetrationFactor = SNIPER_PENETRATION_FACTOR;
                break;
            case RIFLE:
                penetrationFactor = RIFLE_PENETRATION_FACTOR;
                break;
            case SMG:
                penetrationFactor = SMG_PENETRATION_FACTOR;
                break;
            case PISTOL:
                penetrationFactor = PISTOL_PENETRATION_FACTOR;
                break;
            case SHOTGUN:
                penetrationFactor = SHOTGUN_PENETRATION_FACTOR;
                break;
            default:
                penetrationFactor = 0.5; // Valor padrão
        }
        
        return damage * penetrationFactor;
    }
    
    /**
     * Obtém o número máximo de blocos que um projétil pode penetrar, com base no tipo de arma.
     * 
     * @param weaponType O tipo de arma
     * @return O número máximo de blocos que podem ser penetrados
     */
    public static int getMaxPenetrationCount(WeaponType weaponType) {
        switch (weaponType) {
            case SNIPER:
                return 3; // Snipers podem penetrar até 3 blocos
            case RIFLE:
                return 2; // Rifles podem penetrar até 2 blocos
            case SHOTGUN:
                return 1; // Shotguns podem penetrar 1 bloco
            case SMG:
                return 1; // SMGs podem penetrar 1 bloco
            case PISTOL:
                return 1; // Pistolas podem penetrar 1 bloco
            default:
                return 0;
        }
    }
}
