package org.esg.commands;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.esg.Manager.ArmorManager;
import org.esg.enums.ArmorPiece;
import org.esg.enums.ArmorType;
import org.esg.models.ArmorFactory;

import java.util.Map;

/**
 * Comando para gerenciar armaduras.
 */
public class ArmorCommand implements CommandExecutor {

    private static final String USAGE_MESSAGE = ChatColor.RED + "Uso: /armor <leather|kevlar> [jogador]";
    private static final String REMOVE_USAGE_MESSAGE = ChatColor.RED + "Uso: /armor remove [helmet|chestplate|leggings|boots|all] [jogador]";
    private static final String ONLY_PLAYERS_MESSAGE = ChatColor.RED + "Este comando só pode ser usado por jogadores!";
    private static final String PLAYER_NOT_FOUND_MESSAGE = ChatColor.RED + "Jogador não encontrado: %s";
    private static final String INVALID_ARMOR_MESSAGE = ChatColor.RED + "Tipo de armadura inválido. Use: leather ou kevlar";
    private static final String INVALID_PIECE_MESSAGE = ChatColor.RED + "Peça de armadura inválida. Use: helmet, chestplate, leggings, boots ou all";

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sender.sendMessage(USAGE_MESSAGE);
            sender.sendMessage(REMOVE_USAGE_MESSAGE);
            sender.sendMessage(ChatColor.YELLOW + "Ou use: /armor menu - Para abrir o menu de gerenciamento de armaduras");
            return true;
        }

        // Comando para abrir o menu de armaduras
        if (args[0].equalsIgnoreCase("menu")) {
            if (!(sender instanceof Player)) {
                sender.sendMessage(ONLY_PLAYERS_MESSAGE);
                return true;
            }
            
            Player player = (Player) sender;
            org.esg.ui.ArmorMenu.open(player);
            return true;
        }

        // Comando para remover armadura
        if (args[0].equalsIgnoreCase("remove")) {
            return handleRemoveCommand(sender, args);
        }

        // Comando para equipar armadura
        String armorTypeName = args[0].toLowerCase();
        ArmorType armorType = ArmorFactory.getArmorTypeByName(armorTypeName);

        if (armorType == null) {
            sender.sendMessage(INVALID_ARMOR_MESSAGE);
            return true;
        }

        Player target;

        if (args.length >= 2) {
            // Se houver um segundo argumento, é o nome do jogador alvo
            target = Bukkit.getPlayer(args[1]);

            if (target == null) {
                sender.sendMessage(String.format(PLAYER_NOT_FOUND_MESSAGE, args[1]));
                return true;
            }
        } else {
            // Se não houver um segundo argumento, o alvo é o próprio jogador
            if (!(sender instanceof Player)) {
                sender.sendMessage(ONLY_PLAYERS_MESSAGE);
                return true;
            }

            target = (Player) sender;
        }

        // Equipar o conjunto completo de armadura no jogador
        ArmorManager.equipArmorSet(target, armorType);

        // Notificar o remetente se for diferente do alvo
        if (sender != target) {
            sender.sendMessage(ChatColor.GREEN + "Você deu um conjunto completo de armadura " +
                              armorType.getDisplayName() + " para " + target.getName() + "!");
        }

        return true;
    }

    /**
     * Manipula o comando para remover armadura.
     *
     * @param sender O remetente do comando
     * @param args Os argumentos do comando
     * @return true se o comando foi executado com sucesso
     */
    private boolean handleRemoveCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(REMOVE_USAGE_MESSAGE);
            return true;
        }

        String pieceName = args[1].toLowerCase();

        Player target;

        if (args.length >= 3) {
            // Se houver um terceiro argumento, é o nome do jogador alvo
            target = Bukkit.getPlayer(args[2]);

            if (target == null) {
                sender.sendMessage(String.format(PLAYER_NOT_FOUND_MESSAGE, args[2]));
                return true;
            }
        } else {
            // Se não houver um terceiro argumento, o alvo é o próprio jogador
            if (!(sender instanceof Player)) {
                sender.sendMessage(ONLY_PLAYERS_MESSAGE);
                return true;
            }

            target = (Player) sender;
        }

        // Remover a armadura especificada
        if (pieceName.equals("all")) {
            ArmorManager.removeAllArmor(target);

            if (sender != target) {
                sender.sendMessage(ChatColor.GREEN + "Você removeu todas as armaduras de " + target.getName() + "!");
            }
        } else {
            ArmorPiece piece = getArmorPieceByName(pieceName);

            if (piece == null) {
                sender.sendMessage(INVALID_PIECE_MESSAGE);
                return true;
            }

            ArmorManager.removeArmorPiece(target, piece);

            if (sender != target) {
                sender.sendMessage(ChatColor.GREEN + "Você removeu a " + piece.getDisplayName().toLowerCase() +
                                  " de " + target.getName() + "!");
            }
        }

        return true;
    }

    /**
     * Obtém a peça de armadura pelo nome.
     *
     * @param name O nome da peça de armadura
     * @return A peça de armadura, ou null se o nome for inválido
     */
    private ArmorPiece getArmorPieceByName(String name) {
        if (name.equalsIgnoreCase("helmet") || name.equalsIgnoreCase("capacete")) {
            return ArmorPiece.HELMET;
        } else if (name.equalsIgnoreCase("chestplate") || name.equalsIgnoreCase("peitoral")) {
            return ArmorPiece.CHESTPLATE;
        } else if (name.equalsIgnoreCase("leggings") || name.equalsIgnoreCase("calças") ||
                  name.equalsIgnoreCase("calcas")) {
            return ArmorPiece.LEGGINGS;
        } else if (name.equalsIgnoreCase("boots") || name.equalsIgnoreCase("botas")) {
            return ArmorPiece.BOOTS;
        }

        return null;
    }
}
