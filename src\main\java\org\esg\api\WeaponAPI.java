package org.esg.api;

import org.bukkit.Material;
import org.bukkit.block.Chest;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.esg.Manager.WeaponManager;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.utils.WeaponUtils;
import org.esg.weapons.WeaponFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * API pública para interação com o sistema de armas.
 * Esta classe fornece métodos para outros plugins acessarem e utilizarem
 * as funcionalidades do sistema de armas.
 */
public final class WeaponAPI {
    private static final Logger LOGGER = Logger.getLogger(WeaponAPI.class.getName());

    private WeaponAPI() {
        // Construtor privado para evitar instanciação
    }

    /**
     * Obtém uma lista com os nomes de todas as armas disponíveis.
     *
     * @return Lista de nomes de armas
     */
    public static List<String> getAvailableWeapons() {
        return WeaponFactory.getWeaponRegistry().keySet().stream()
                .filter(name -> !name.contains("-") && !name.contains(" ")) // Filtrar nomes duplicados em minúsculas
                .collect(Collectors.toList());
    }

    /**
     * Cria uma instância de arma pelo nome.
     *
     * @param weaponName Nome da arma
     * @return Instância da arma ou null se não existir
     */
    public static Weapon createWeapon(String weaponName) {
        try {
            LOGGER.info("WeaponAPI: Tentando criar arma: " + weaponName);
            Weapon weapon = WeaponFactory.createWeapon(weaponName);
            LOGGER.info("WeaponAPI: Arma criada com sucesso: " + weapon.getName());
            return weapon;
        } catch (IllegalArgumentException e) {
            LOGGER.warning("WeaponAPI: Erro ao criar arma '" + weaponName + "': " + e.getMessage());
            return null;
        }
    }

    /**
     * Dá uma arma a um jogador.
     *
     * @param player Jogador que receberá a arma
     * @param weaponName Nome da arma
     * @return true se a arma foi dada com sucesso, false caso contrário
     */
    public static boolean giveWeaponToPlayer(Player player, String weaponName) {
        try {
            LOGGER.info("WeaponAPI: Tentando dar arma '" + weaponName + "' para jogador " + player.getName());
            
            // Verificação especial para Barrett
            if (weaponName.equalsIgnoreCase("Barrett")) {
                LOGGER.info("WeaponAPI: Processando caso especial para Barrett");
            }
            
            Weapon weapon = WeaponFactory.createWeapon(weaponName);
            
            if (weapon == null) {
                LOGGER.warning("WeaponAPI: Falha ao criar arma '" + weaponName + "'");
                return false;
            }
            
            LOGGER.info("WeaponAPI: Arma criada com sucesso: " + weapon.getName() + 
                       ", Tipo: " + weapon.getType() + 
                       ", Munição: " + weapon.getCurrentAmmo() + "/" + weapon.getMaxAmmo());
            
            WeaponManager.giveWeapon(player, weapon);
            LOGGER.info("WeaponAPI: Arma '" + weaponName + "' dada com sucesso para " + player.getName());
            return true;
            
        } catch (Exception e) {
            LOGGER.severe("WeaponAPI: Erro ao dar arma '" + weaponName + "' para jogador " + player.getName() + 
                         ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Verifica se um item é uma arma.
     *
     * @param item Item a ser verificado
     * @param player Jogador que possui o item
     * @return true se o item é uma arma, false caso contrário
     */
    public static boolean isWeapon(ItemStack item, Player player) {
        return WeaponUtils.getWeaponFromItem(item, player) != null;
    }

    /**
     * Obtém a arma a partir de um item.
     *
     * @param item Item a ser verificado
     * @param player Jogador que possui o item
     * @return A arma ou null se o item não for uma arma
     */
    public static Weapon getWeaponFromItem(ItemStack item, Player player) {
        return WeaponUtils.getWeaponFromItem(item, player);
    }

    /**
     * Obtém o nome da arma a partir de um item.
     *
     * @param item Item a ser verificado
     * @param player Jogador que possui o item
     * @return Nome da arma ou null se o item não for uma arma
     */
    public static String getWeaponName(ItemStack item, Player player) {
        Weapon weapon = getWeaponFromItem(item, player);
        return weapon != null ? weapon.getName() : null;
    }

    /**
     * Obtém o dano base da arma.
     *
     * @param weaponName Nome da arma
     * @return Dano base da arma ou -1 se a arma não existir
     */
    public static double getWeaponDamage(String weaponName) {
        Weapon weapon = createWeapon(weaponName);
        return weapon != null ? weapon.getDamage() : -1;
    }

    /**
     * Cria um ItemStack de arma pronto para ser usado.
     * Este método é útil para plugins que querem criar itens de arma
     * para colocar em baús ou dar a jogadores.
     *
     * @param weaponName Nome da arma
     * @param player Jogador (usado para NBT)
     * @return ItemStack da arma ou null se a arma não existir
     */
    public static ItemStack createWeaponItem(String weaponName, Player player) {
        try {
            Weapon weapon = WeaponFactory.createWeapon(weaponName);
            ItemStack item = WeaponFactory.toItemStack(weapon);
            return WeaponUtils.applyWeaponToItem(item, weapon, player);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Adiciona uma arma a um baú.
     * Este método é útil para plugins que querem adicionar armas a baús de loot.
     *
     * @param chest Baú onde a arma será adicionada
     * @param weaponName Nome da arma
     * @param player Jogador (usado para NBT)
     * @param slot Slot do baú onde a arma será adicionada (-1 para slot aleatório)
     * @return true se a arma foi adicionada com sucesso, false caso contrário
     */
    public static boolean addWeaponToChest(Chest chest, String weaponName, Player player, int slot) {
        ItemStack weaponItem = createWeaponItem(weaponName, player);
        if (weaponItem == null) return false;

        Inventory inventory = chest.getInventory();

        // Se o slot for -1, escolher um slot aleatório
        if (slot == -1) {
            slot = new Random().nextInt(inventory.getSize());
        }

        // Verificar se o slot é válido
        if (slot < 0 || slot >= inventory.getSize()) {
            return false;
        }

        // Adicionar a arma ao baú
        inventory.setItem(slot, weaponItem);
        return true;
    }

    /**
     * Adiciona uma arma aleatória a um baú com base em uma tabela de probabilidades.
     * Este método é útil para plugins que querem adicionar armas aleatórias a baús de loot.
     *
     * @param chest Baú onde a arma será adicionada
     * @param player Jogador (usado para NBT)
     * @param weaponProbabilities Mapa com os nomes das armas e suas probabilidades (deve somar 1.0)
     * @param slot Slot do baú onde a arma será adicionada (-1 para slot aleatório)
     * @return true se a arma foi adicionada com sucesso, false caso contrário
     */
    public static boolean addRandomWeaponToChest(Chest chest, Player player, Map<String, Double> weaponProbabilities, int slot) {
        String selectedWeapon = selectRandomWeapon(weaponProbabilities);
        if (selectedWeapon == null) return false;

        return addWeaponToChest(chest, selectedWeapon, player, slot);
    }

    /**
     * Seleciona uma arma aleatória com base em uma tabela de probabilidades.
     *
     * @param weaponProbabilities Mapa com os nomes das armas e suas probabilidades (deve somar 1.0)
     * @return Nome da arma selecionada ou null se nenhuma foi selecionada
     */
    public static String selectRandomWeapon(Map<String, Double> weaponProbabilities) {
        double randomValue = new Random().nextDouble();
        double cumulativeProbability = 0.0;

        for (Map.Entry<String, Double> entry : weaponProbabilities.entrySet()) {
            cumulativeProbability += entry.getValue();
            if (randomValue <= cumulativeProbability) {
                return entry.getKey();
            }
        }

        // Caso as probabilidades não somem 1.0, retornar null
        return null;
    }

    /**
     * Obtém informações detalhadas sobre uma arma.
     * Este método é útil para plugins que querem exibir informações sobre armas.
     *
     * @param weaponName Nome da arma
     * @return Mapa com informações da arma ou null se a arma não existir
     */
    public static Map<String, Object> getWeaponInfo(String weaponName) {
        Weapon weapon = createWeapon(weaponName);
        if (weapon == null) return null;

        Map<String, Object> info = new HashMap<>();
        info.put("name", weapon.getName());
        info.put("type", weapon.getType().name());
        info.put("ammoType", weapon.getAmmoType().name());
        info.put("damage", weapon.getDamage());
        info.put("range", weapon.getRange());
        info.put("accuracy", weapon.getAccuracy());
        info.put("fireRate", weapon.getFireRate());
        info.put("maxAmmo", weapon.getMaxAmmo());

        return info;
    }

    /**
     * Obtém o tipo de uma arma.
     *
     * @param weaponName Nome da arma
     * @return Tipo da arma ou null se a arma não existir
     */
    public static WeaponType getWeaponType(String weaponName) {
        Weapon weapon = createWeapon(weaponName);
        return weapon != null ? weapon.getType() : null;
    }

    /**
     * Obtém o tipo de munição de uma arma.
     *
     * @param weaponName Nome da arma
     * @return Tipo de munição da arma ou null se a arma não existir
     */
    public static AmmoType getWeaponAmmoType(String weaponName) {
        Weapon weapon = createWeapon(weaponName);
        return weapon != null ? weapon.getAmmoType() : null;
    }

    /**
     * Cria um item de munição para uma arma específica.
     * Este método é útil para plugins que querem adicionar munição a baús de loot.
     *
     * @param ammoType Tipo de munição
     * @param amount Quantidade de munição
     * @return ItemStack da munição
     */
    public static ItemStack createAmmoItem(AmmoType ammoType, int amount) {
        // Implementar a criação de itens de munição
        // Esta é uma implementação básica, você pode adaptá-la conforme necessário
        Material material;
        String displayName;

        switch (ammoType) {
            case _9MM:
                material = Material.GOLD_NUGGET;
                displayName = "§r§eMunição 9mm";
                break;
            case _556MM:
                material = Material.IRON_INGOT; // Usando IRON_INGOT em vez de IRON_NUGGET que não existe no 1.8.8
                displayName = "§r§7Munição 5.56mm";
                break;
            case _762MM:
                material = Material.IRON_INGOT;
                displayName = "§r§7Munição 7.62mm";
                break;
            case _50CAL:
                material = Material.GOLD_INGOT;
                displayName = "§r§6Munição .50 Cal";
                break;
            case _12GAUGE:
                material = Material.CLAY_BALL;
                displayName = "§r§cMunição de Shotgun";
                break;
            default:
                return null;
        }

        ItemStack ammoItem = new ItemStack(material, amount);
        org.bukkit.inventory.meta.ItemMeta meta = ammoItem.getItemMeta();
        meta.setDisplayName(displayName);

        List<String> lore = new ArrayList<>();
        lore.add("§r§7Tipo: " + ammoType.name().replace("_", "."));
        lore.add("§r§7Quantidade: §a" + amount);
        meta.setLore(lore);

        ammoItem.setItemMeta(meta);
        return ammoItem;
    }

    /**
     * Adiciona munição a um baú.
     * Este método é útil para plugins que querem adicionar munição a baús de loot.
     *
     * @param chest Baú onde a munição será adicionada
     * @param ammoType Tipo de munição
     * @param amount Quantidade de munição
     * @param slot Slot do baú onde a munição será adicionada (-1 para slot aleatório)
     * @return true se a munição foi adicionada com sucesso, false caso contrário
     */
    public static boolean addAmmoToChest(Chest chest, AmmoType ammoType, int amount, int slot) {
        ItemStack ammoItem = createAmmoItem(ammoType, amount);
        if (ammoItem == null) return false;

        Inventory inventory = chest.getInventory();

        // Se o slot for -1, escolher um slot aleatório
        if (slot == -1) {
            slot = new Random().nextInt(inventory.getSize());
        }

        // Verificar se o slot é válido
        if (slot < 0 || slot >= inventory.getSize()) {
            return false;
        }

        // Adicionar a munição ao baú
        inventory.setItem(slot, ammoItem);
        return true;
    }

    /**
     * Adiciona munição aleatória a um baú com base em uma tabela de probabilidades.
     * Este método é útil para plugins que querem adicionar munição aleatória a baús de loot.
     *
     * @param chest Baú onde a munição será adicionada
     * @param ammoProbabilities Mapa com os tipos de munição e suas probabilidades (deve somar 1.0)
     * @param minAmount Quantidade mínima de munição
     * @param maxAmount Quantidade máxima de munição
     * @param slot Slot do baú onde a munição será adicionada (-1 para slot aleatório)
     * @return true se a munição foi adicionada com sucesso, false caso contrário
     */
    public static boolean addRandomAmmoToChest(Chest chest, Map<AmmoType, Double> ammoProbabilities, int minAmount, int maxAmount, int slot) {
        // Selecionar um tipo de munição aleatório com base nas probabilidades
        double randomValue = new Random().nextDouble();
        double cumulativeProbability = 0.0;
        AmmoType selectedAmmoType = null;

        for (Map.Entry<AmmoType, Double> entry : ammoProbabilities.entrySet()) {
            cumulativeProbability += entry.getValue();
            if (randomValue <= cumulativeProbability) {
                selectedAmmoType = entry.getKey();
                break;
            }
        }

        if (selectedAmmoType == null) return false;

        // Gerar uma quantidade aleatória de munição entre minAmount e maxAmount
        int amount = minAmount;
        if (maxAmount > minAmount) {
            amount += new Random().nextInt(maxAmount - minAmount + 1);
        }

        // Adicionar a munição ao baú
        return addAmmoToChest(chest, selectedAmmoType, amount, slot);
    }
}
