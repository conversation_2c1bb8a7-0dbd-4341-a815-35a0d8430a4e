<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitpack.io" />
      <option name="name" value="jitpack.io" />
      <option name="url" value="https://jitpack.io" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="https://oss.sonatype.org/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spigot-repo" />
      <option name="name" value="spigot-repo" />
      <option name="url" value="https://hub.spigotmc.org/nexus/content/repositories/snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="codemc-repo" />
      <option name="name" value="codemc-repo" />
      <option name="url" value="https://repo.codemc.io/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spigotmc-repo" />
      <option name="name" value="spigotmc-repo" />
      <option name="url" value="https://hub.spigotmc.org/nexus/content/repositories/snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="bukkit-repo" />
      <option name="name" value="bukkit-repo" />
      <option name="url" value="http://repo.bukkit.org/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="bukkit-repo" />
      <option name="name" value="bukkit-repo" />
      <option name="url" value="https://hub.spigotmc.org/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spigotmc-local" />
      <option name="name" value="spigotmc-local" />
      <option name="url" value="file://C:\Users\<USER>