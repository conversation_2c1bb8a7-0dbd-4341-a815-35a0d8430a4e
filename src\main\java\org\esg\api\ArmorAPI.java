package org.esg.api;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.esg.Manager.ArmorManager;
import org.esg.enums.ArmorPiece;
import org.esg.enums.ArmorType;
import org.esg.models.Armor;
import org.esg.models.ArmorFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API pública para interação com o sistema de armaduras.
 * Esta classe fornece métodos para outros plugins acessarem e utilizarem
 * as funcionalidades do sistema de armaduras.
 */
public final class ArmorAPI {

    private ArmorAPI() {
        // Construtor privado para evitar instanciação
    }

    /**
     * Obtém uma lista com os tipos de armaduras disponíveis.
     *
     * @return Lista de tipos de armaduras
     */
    public static List<String> getAvailableArmorTypes() {
        return Arrays.stream(ArmorType.values())
                .map(ArmorType::name)
                .collect(Collectors.toList());
    }

    /**
     * Obtém uma lista com as peças de armaduras disponíveis.
     *
     * @return Lista de peças de armaduras
     */
    public static List<String> getAvailableArmorPieces() {
        return Arrays.stream(ArmorPiece.values())
                .map(ArmorPiece::name)
                .collect(Collectors.toList());
    }

    /**
     * Cria uma peça de armadura pelo tipo e peça.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @param armorPiece A peça de armadura (HELMET, CHESTPLATE, LEGGINGS, BOOTS)
     * @return A peça de armadura criada ou null se os parâmetros forem inválidos
     */
    public static Armor createArmorPiece(String armorType, String armorPiece) {
        try {
            ArmorType type = ArmorType.valueOf(armorType.toUpperCase());
            ArmorPiece piece = ArmorPiece.valueOf(armorPiece.toUpperCase());
            return ArmorFactory.createArmorPiece(type, piece);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Cria um capacete do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return O capacete criado ou null se o tipo for inválido
     */
    public static Armor createHelmet(String armorType) {
        return createArmorPiece(armorType, "HELMET");
    }

    /**
     * Cria um peitoral do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return O peitoral criado ou null se o tipo for inválido
     */
    public static Armor createChestplate(String armorType) {
        return createArmorPiece(armorType, "CHESTPLATE");
    }

    /**
     * Cria calças do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return As calças criadas ou null se o tipo for inválido
     */
    public static Armor createLeggings(String armorType) {
        return createArmorPiece(armorType, "LEGGINGS");
    }

    /**
     * Cria botas do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return As botas criadas ou null se o tipo for inválido
     */
    public static Armor createBoots(String armorType) {
        return createArmorPiece(armorType, "BOOTS");
    }

    /**
     * Cria um capacete de couro.
     *
     * @return O capacete de couro
     */
    public static Armor createLeatherHelmet() {
        return ArmorFactory.createLeatherArmorPiece(ArmorPiece.HELMET);
    }

    /**
     * Cria um peitoral de couro.
     *
     * @return O peitoral de couro
     */
    public static Armor createLeatherChestplate() {
        return ArmorFactory.createLeatherArmorPiece(ArmorPiece.CHESTPLATE);
    }

    /**
     * Cria calças de couro.
     *
     * @return As calças de couro
     */
    public static Armor createLeatherLeggings() {
        return ArmorFactory.createLeatherArmorPiece(ArmorPiece.LEGGINGS);
    }

    /**
     * Cria botas de couro.
     *
     * @return As botas de couro
     */
    public static Armor createLeatherBoots() {
        return ArmorFactory.createLeatherArmorPiece(ArmorPiece.BOOTS);
    }

    /**
     * Cria um capacete Kevlar.
     *
     * @return O capacete Kevlar
     */
    public static Armor createKevlarHelmet() {
        return ArmorFactory.createKevlarArmorPiece(ArmorPiece.HELMET);
    }

    /**
     * Cria um peitoral Kevlar.
     *
     * @return O peitoral Kevlar
     */
    public static Armor createKevlarChestplate() {
        return ArmorFactory.createKevlarArmorPiece(ArmorPiece.CHESTPLATE);
    }

    /**
     * Cria calças Kevlar.
     *
     * @return As calças Kevlar
     */
    public static Armor createKevlarLeggings() {
        return ArmorFactory.createKevlarArmorPiece(ArmorPiece.LEGGINGS);
    }

    /**
     * Cria botas Kevlar.
     *
     * @return As botas Kevlar
     */
    public static Armor createKevlarBoots() {
        return ArmorFactory.createKevlarArmorPiece(ArmorPiece.BOOTS);
    }

    /**
     * Cria um conjunto completo de armadura pelo tipo.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return Um mapa com todas as peças de armadura ou null se o tipo for inválido
     */
    public static Map<ArmorPiece, Armor> createArmorSet(String armorType) {
        try {
            ArmorType type = ArmorType.valueOf(armorType.toUpperCase());
            return ArmorFactory.createArmorSet(type);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Equipa uma peça de armadura em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @param armorPiece A peça de armadura (HELMET, CHESTPLATE, LEGGINGS, BOOTS)
     * @return true se a armadura foi equipada com sucesso, false caso contrário
     */
    public static boolean equipArmorPiece(Player player, String armorType, String armorPiece) {
        Armor armor = createArmorPiece(armorType, armorPiece);
        if (armor == null) {
            return false;
        }

        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa um capacete em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return true se o capacete foi equipado com sucesso, false caso contrário
     */
    public static boolean equipHelmet(Player player, String armorType) {
        return equipArmorPiece(player, armorType, "HELMET");
    }

    /**
     * Equipa um peitoral em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return true se o peitoral foi equipado com sucesso, false caso contrário
     */
    public static boolean equipChestplate(Player player, String armorType) {
        return equipArmorPiece(player, armorType, "CHESTPLATE");
    }

    /**
     * Equipa calças em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return true se as calças foram equipadas com sucesso, false caso contrário
     */
    public static boolean equipLeggings(Player player, String armorType) {
        return equipArmorPiece(player, armorType, "LEGGINGS");
    }

    /**
     * Equipa botas em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return true se as botas foram equipadas com sucesso, false caso contrário
     */
    public static boolean equipBoots(Player player, String armorType) {
        return equipArmorPiece(player, armorType, "BOOTS");
    }

    /**
     * Equipa um capacete de couro em um jogador.
     *
     * @param player O jogador
     * @return true se o capacete foi equipado com sucesso
     */
    public static boolean equipLeatherHelmet(Player player) {
        Armor armor = createLeatherHelmet();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa um peitoral de couro em um jogador.
     *
     * @param player O jogador
     * @return true se o peitoral foi equipado com sucesso
     */
    public static boolean equipLeatherChestplate(Player player) {
        Armor armor = createLeatherChestplate();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa calças de couro em um jogador.
     *
     * @param player O jogador
     * @return true se as calças foram equipadas com sucesso
     */
    public static boolean equipLeatherLeggings(Player player) {
        Armor armor = createLeatherLeggings();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa botas de couro em um jogador.
     *
     * @param player O jogador
     * @return true se as botas foram equipadas com sucesso
     */
    public static boolean equipLeatherBoots(Player player) {
        Armor armor = createLeatherBoots();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa um capacete Kevlar em um jogador.
     *
     * @param player O jogador
     * @return true se o capacete foi equipado com sucesso
     */
    public static boolean equipKevlarHelmet(Player player) {
        Armor armor = createKevlarHelmet();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa um peitoral Kevlar em um jogador.
     *
     * @param player O jogador
     * @return true se o peitoral foi equipado com sucesso
     */
    public static boolean equipKevlarChestplate(Player player) {
        Armor armor = createKevlarChestplate();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa calças Kevlar em um jogador.
     *
     * @param player O jogador
     * @return true se as calças foram equipadas com sucesso
     */
    public static boolean equipKevlarLeggings(Player player) {
        Armor armor = createKevlarLeggings();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa botas Kevlar em um jogador.
     *
     * @param player O jogador
     * @return true se as botas foram equipadas com sucesso
     */
    public static boolean equipKevlarBoots(Player player) {
        Armor armor = createKevlarBoots();
        ArmorManager.equipArmorPiece(player, armor);
        return true;
    }

    /**
     * Equipa um conjunto completo de armadura em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return true se a armadura foi equipada com sucesso, false caso contrário
     */
    public static boolean equipArmorSet(Player player, String armorType) {
        try {
            ArmorType type = ArmorType.valueOf(armorType.toUpperCase());
            ArmorManager.equipArmorSet(player, type);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Remove uma peça específica de armadura de um jogador.
     *
     * @param player O jogador
     * @param armorPiece A peça de armadura (HELMET, CHESTPLATE, LEGGINGS, BOOTS)
     * @return true se a armadura foi removida com sucesso, false caso contrário
     */
    public static boolean removeArmorPiece(Player player, String armorPiece) {
        try {
            ArmorPiece piece = ArmorPiece.valueOf(armorPiece.toUpperCase());
            ArmorManager.removeArmorPiece(player, piece);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Remove todas as armaduras de um jogador.
     *
     * @param player O jogador
     */
    public static void removeAllArmor(Player player) {
        ArmorManager.removeAllArmor(player);
    }

    /**
     * Obtém o conjunto de armadura atual de um jogador.
     *
     * @param player O jogador
     * @return Um mapa com as peças de armadura equipadas ou null se não tiver armadura
     */
    public static Map<String, Armor> getPlayerArmorSet(Player player) {
        Map<ArmorPiece, Armor> armorSet = ArmorManager.getPlayerArmorSet(player);
        if (armorSet == null) {
            return null;
        }

        Map<String, Armor> result = new HashMap<>();
        for (Map.Entry<ArmorPiece, Armor> entry : armorSet.entrySet()) {
            result.put(entry.getKey().name(), entry.getValue());
        }

        return result;
    }

    /**
     * Calcula o dano reduzido pela armadura do jogador.
     *
     * @param player O jogador
     * @param damage O dano original
     * @return O dano reduzido
     */
    public static double calculateReducedDamage(Player player, double damage) {
        return ArmorManager.calculateReducedDamage(player, damage);
    }

    /**
     * Obtém informações detalhadas sobre uma peça de armadura.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @param armorPiece A peça de armadura (HELMET, CHESTPLATE, LEGGINGS, BOOTS)
     * @return Mapa com informações da armadura ou null se os parâmetros forem inválidos
     */
    public static Map<String, Object> getArmorInfo(String armorType, String armorPiece) {
        Armor armor = createArmorPiece(armorType, armorPiece);
        if (armor == null) {
            return null;
        }

        Map<String, Object> info = new HashMap<>();
        info.put("name", armor.getName());
        info.put("type", armor.getType().name());
        info.put("piece", armor.getPiece().name());
        info.put("damageReduction", armor.getDamageReduction());
        info.put("durability", armor.getDurability());

        return info;
    }

    /**
     * Converte uma peça de armadura em um ItemStack do Bukkit.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @param armorPiece A peça de armadura (HELMET, CHESTPLATE, LEGGINGS, BOOTS)
     * @return O ItemStack da armadura ou null se os parâmetros forem inválidos
     */
    public static ItemStack toItemStack(String armorType, String armorPiece) {
        Armor armor = createArmorPiece(armorType, armorPiece);
        if (armor == null) {
            return null;
        }

        return armor.createItem();
    }

    /**
     * Obtém um ItemStack de capacete do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return O ItemStack do capacete ou null se o tipo for inválido
     */
    public static ItemStack getHelmetItemStack(String armorType) {
        return toItemStack(armorType, "HELMET");
    }

    /**
     * Obtém um ItemStack de peitoral do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return O ItemStack do peitoral ou null se o tipo for inválido
     */
    public static ItemStack getChestplateItemStack(String armorType) {
        return toItemStack(armorType, "CHESTPLATE");
    }

    /**
     * Obtém um ItemStack de calças do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return O ItemStack das calças ou null se o tipo for inválido
     */
    public static ItemStack getLeggingsItemStack(String armorType) {
        return toItemStack(armorType, "LEGGINGS");
    }

    /**
     * Obtém um ItemStack de botas do tipo especificado.
     *
     * @param armorType O tipo de armadura (LEATHER, KEVLAR)
     * @return O ItemStack das botas ou null se o tipo for inválido
     */
    public static ItemStack getBootsItemStack(String armorType) {
        return toItemStack(armorType, "BOOTS");
    }

    /**
     * Obtém um ItemStack de capacete de couro.
     *
     * @return O ItemStack do capacete de couro
     */
    public static ItemStack getLeatherHelmetItemStack() {
        return createLeatherHelmet().createItem();
    }

    /**
     * Obtém um ItemStack de peitoral de couro.
     *
     * @return O ItemStack do peitoral de couro
     */
    public static ItemStack getLeatherChestplateItemStack() {
        return createLeatherChestplate().createItem();
    }

    /**
     * Obtém um ItemStack de calças de couro.
     *
     * @return O ItemStack das calças de couro
     */
    public static ItemStack getLeatherLeggingsItemStack() {
        return createLeatherLeggings().createItem();
    }

    /**
     * Obtém um ItemStack de botas de couro.
     *
     * @return O ItemStack das botas de couro
     */
    public static ItemStack getLeatherBootsItemStack() {
        return createLeatherBoots().createItem();
    }

    /**
     * Obtém um ItemStack de capacete Kevlar.
     *
     * @return O ItemStack do capacete Kevlar
     */
    public static ItemStack getKevlarHelmetItemStack() {
        return createKevlarHelmet().createItem();
    }

    /**
     * Obtém um ItemStack de peitoral Kevlar.
     *
     * @return O ItemStack do peitoral Kevlar
     */
    public static ItemStack getKevlarChestplateItemStack() {
        return createKevlarChestplate().createItem();
    }

    /**
     * Obtém um ItemStack de calças Kevlar.
     *
     * @return O ItemStack das calças Kevlar
     */
    public static ItemStack getKevlarLeggingsItemStack() {
        return createKevlarLeggings().createItem();
    }

    /**
     * Obtém um ItemStack de botas Kevlar.
     *
     * @return O ItemStack das botas Kevlar
     */
    public static ItemStack getKevlarBootsItemStack() {
        return createKevlarBoots().createItem();
    }
}
