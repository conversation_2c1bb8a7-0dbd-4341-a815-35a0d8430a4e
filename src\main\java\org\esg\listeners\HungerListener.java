package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.FoodLevelChangeEvent;
import org.bukkit.event.player.PlayerJoinEvent;

/**
 * Listener para desativar a fome no jogo.
 * Mantém o nível de fome dos jogadores sempre no máximo.
 */
public class HungerListener implements Listener {
    
    /**
     * Cancela qualquer mudança no nível de fome dos jogadores.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onFoodLevelChange(FoodLevelChangeEvent event) {
        // Cancelar o evento para evitar qualquer mudança no nível de fome
        event.setCancelled(true);
        
        // Garantir que o nível de fome permaneça no máximo (20)
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            player.setFoodLevel(20);
            player.setSaturation(20.0f);
            player.setExhaustion(0.0f);
        }
    }
    
    /**
     * Define o nível de fome para o máximo quando um jogador entra no servidor.
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        player.setFoodLevel(20);
        player.setSaturation(20.0f);
        player.setExhaustion(0.0f);
    }
}
