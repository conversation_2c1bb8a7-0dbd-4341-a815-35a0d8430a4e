package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;

public class AK47 extends Weapon {

    public AK47() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis
        super("AK-47", WeaponType.RIFLE, AmmoType._762MM, 0.5, 80, 0.25, 5, 120, 32, 32, 3, 1);

        // A AK47 tem:
        // - <PERSON><PERSON> menor (0.1 vs 0.8 da Shotgun)
        // - Alcance maior (80 vs 25 da Shotgun)
        // - Precis<PERSON> muito maior (0.7 vs 0.1 da Shotgun) - Isso causa um espalhamento menor
        // - Velocidade de tiro muito maior (10 vs 1.0 da Shotgun)
        // - Velocidade de projétil similar (90 vs 80 da Shotgun)
        // - 5 projéteis por tiro (vs 12 da Shotgun)
        // - Espalhamento natural em cone baseado na alta precisão
    }
}
