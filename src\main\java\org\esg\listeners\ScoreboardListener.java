package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.esg.Manager.ScoreboardManager;

/**
 * Listener para eventos relacionados ao sistema de scoreboard.
 */
public class ScoreboardListener implements Listener {
    
    /**
     * Cria um scoreboard para um jogador quando ele entra no servidor.
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        ScoreboardManager.createScoreboard(player);
    }
    
    /**
     * Remove o scoreboard de um jogador quando ele sai do servidor.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        ScoreboardManager.removeScoreboard(player);
    }
    
    /**
     * Atualiza o scoreboard quando um jogador causa dano a outro.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerDamage(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player) {
            Player player = (Player) event.getDamager();
            ScoreboardManager.updateScoreboard(player);
        }
        
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            ScoreboardManager.updateScoreboard(player);
        }
    }
    
    /**
     * Atualiza o scoreboard quando um jogador morre.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerDeath(EntityDeathEvent event) {
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            ScoreboardManager.updateScoreboard(player);
        }
    }
}
