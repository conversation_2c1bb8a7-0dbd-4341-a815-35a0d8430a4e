package org.esg.weapons;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;
import org.esg.weapons.M4A1;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import java.util.logging.Logger;

/**
 * Factory class for creating and managing weapons.
 */
public final class WeaponFactory {

    private static final Logger LOGGER = Logger.getLogger(WeaponFactory.class.getName());
    private static final Map<String, Supplier<Weapon>> WEAPON_REGISTRY = new HashMap<>();
    private static final Map<String, Material> WEAPON_MATERIALS = new HashMap<>();

    static {
        // Definir materiais específicos para cada arma
        WEAPON_MATERIALS.put("ak-47", Material.STONE_HOE);
        WEAPON_MATERIALS.put("ar-15", Material.IRON_HOE);
        WEAPON_MATERIALS.put("spas-12", Material.WOOD_HOE);
        WEAPON_MATERIALS.put("barrett", Material.DIAMOND_HOE);
        WEAPON_MATERIALS.put("barret", Material.DIAMOND_HOE); // Variação com um "t"
        WEAPON_MATERIALS.put("uzi", Material.GOLD_HOE);
        // Novos materiais para as armas adicionadas
        WEAPON_MATERIALS.put("dragunov", Material.DIAMOND_SPADE);
        WEAPON_MATERIALS.put("lança-chamas", Material.RECORD_10);
        WEAPON_MATERIALS.put("3oitão", Material.GOLD_AXE);
        // Material para M4A1
        WEAPON_MATERIALS.put("m4a1", Material.COMPASS);
        // Material para RPG
        WEAPON_MATERIALS.put("rpg", Material.SADDLE);
        // Material para DMR
        WEAPON_MATERIALS.put("dmr", Material.CARROT_STICK);

        // Registrar armas com seus nomes exatos (case-sensitive)
        registerWeapon("AK-47", AK47::new);
        registerWeapon("Spas-12", Shotgun::new);
        registerWeapon("AR-15", AR15::new);
        registerWeapon("Barrett", Barrett::new);
        registerWeapon("UZI", UZI::new);
        // Registrar as novas armas
        registerWeapon("Dragunov", Dragunov::new);
        registerWeapon("Lança-Chamas", LancaChamas::new);
        registerWeapon("3oitão", Oitao::new);
        // Registrar a M4A1
        registerWeapon("M4A1", M4A1::new);
        // Registrar o RPG
        registerWeapon("RPG", RPG::new);
        // Registrar o DMR
        registerWeapon("DMR", DMR::new);
        
        // Garantir que a Barrett está registrada corretamente com variações de nome
        WEAPON_REGISTRY.put("barrett", WEAPON_REGISTRY.get("Barrett"));
        // Adicionar variações comuns para Barrett com grafia diferente
        WEAPON_REGISTRY.put("barret", WEAPON_REGISTRY.get("Barrett"));
        WEAPON_REGISTRY.put("BARRET", WEAPON_REGISTRY.get("Barrett"));
        WEAPON_REGISTRY.put("BARRETT", WEAPON_REGISTRY.get("Barrett"));
        
        // Log de armas registradas
        LOGGER.info("Armas registradas: " + WEAPON_REGISTRY.keySet());
    }

    private WeaponFactory() {
        // Construtor privado para evitar instanciação.
    }

    public static void registerWeapon(String name, Supplier<Weapon> weaponSupplier) {
        // Registrar com o nome exato para preservar a formatação
        WEAPON_REGISTRY.put(name, weaponSupplier);
        // Também registrar com o nome em minúsculas para facilitar a busca
        WEAPON_REGISTRY.put(name.toLowerCase(), weaponSupplier);
    }

    public static Weapon createWeapon(String weaponName) {
        // Log especial para Barrett
        if (weaponName != null && (weaponName.equalsIgnoreCase("Barrett") || weaponName.equalsIgnoreCase("BARRET") || weaponName.equalsIgnoreCase("Barret"))) {
            LOGGER.info("Tentando criar Barrett/Barret. Fornecedor disponível: " + 
                       (WEAPON_REGISTRY.containsKey("Barrett") || WEAPON_REGISTRY.containsKey("barrett") || 
                        WEAPON_REGISTRY.containsKey("BARRET") || WEAPON_REGISTRY.containsKey("barret")));
        }
        
        // Procurar a arma pelo nome exato primeiro
        Supplier<Weapon> supplier = WEAPON_REGISTRY.get(weaponName);

        // Se não encontrar, tentar com o nome em minúsculas
        if (supplier == null) {
            supplier = WEAPON_REGISTRY.get(weaponName.toLowerCase());
            if (supplier != null) {
                LOGGER.info("Arma encontrada com nome em minúsculas: " + weaponName.toLowerCase());
            }
        }

        // Se ainda não encontrar, tentar com variações comuns
        if (supplier == null) {
            // Remover hífens e espaços
            String simplifiedName = weaponName.toLowerCase().replace("-", "").replace(" ", "");
            LOGGER.info("Tentando nome simplificado: " + simplifiedName);

            // Verificar variações comuns
            if (simplifiedName.equals("ak47")) {
                supplier = WEAPON_REGISTRY.get("AK-47");
            } else if (simplifiedName.equals("spas12")) {
                supplier = WEAPON_REGISTRY.get("Spas-12");
            } else if (simplifiedName.equals("ar15")) {
                supplier = WEAPON_REGISTRY.get("AR-15");
            } else if (simplifiedName.equals("lancachamas")) {
                supplier = WEAPON_REGISTRY.get("Lança-Chamas");
            } else if (simplifiedName.equals("oitao") || simplifiedName.equals("3oitao")) {
                supplier = WEAPON_REGISTRY.get("3oitão");
            } else if (simplifiedName.equals("barrett") || simplifiedName.equals("barret")) {
                // Caso especial para a Barrett
                supplier = WEAPON_REGISTRY.get("Barrett");
                LOGGER.info("Tentando resolver Barrett/Barret via caso especial");
            } else if (simplifiedName.equals("m4a1")) {
                supplier = WEAPON_REGISTRY.get("M4A1");
            } else if (simplifiedName.equals("rpg")) {
                supplier = WEAPON_REGISTRY.get("RPG");
            }
        }

        // Caso especial para Barrett
        if (supplier == null && weaponName != null && 
            (weaponName.equalsIgnoreCase("Barrett") || 
             weaponName.equalsIgnoreCase("BARRET") || 
             weaponName.equalsIgnoreCase("Barret"))) {
            LOGGER.warning("Fornecedor para Barrett/Barret não encontrado, criando manualmente");
            return new Barrett();  // Criar diretamente se não encontrar
        }

        if (supplier == null) {
            LOGGER.warning("Arma desconhecida: " + weaponName);
            throw new IllegalArgumentException("Arma desconhecida: " + weaponName);
        }

        Weapon weapon = supplier.get();
        // Comentado para reduzir log spam
        // LOGGER.info("Arma criada: " + weapon.getName() + " (Tipo: " + weapon.getType() + ")");
        return weapon;
    }

    public static ItemStack toItemStack(Weapon weapon) {
        Material material = getMaterialForWeapon(weapon);
        LOGGER.info("Material para " + weapon.getName() + ": " + material.name());
        
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName("§r" + weapon.getName());
            item.setItemMeta(meta);
        }
        return item;
    }

    public static Map<String, Supplier<Weapon>> getWeaponRegistry() {
        return Collections.unmodifiableMap(WEAPON_REGISTRY);
    }

    private static Material getMaterialForWeapon(Weapon weapon) {
        // Caso especial para Barrett
        if (weapon != null && (weapon.getName().equalsIgnoreCase("Barrett") || 
                              weapon.getName().equalsIgnoreCase("Barret"))) {
            LOGGER.info("Obtendo material especial para Barrett/Barret");
            return Material.DIAMOND_HOE;
        }
        
        // Normalizar o nome da arma para minúsculas
        String weaponName = weapon.getName().toLowerCase();
        
        Material material = null;
        
        // Verificar armas específicas
        if (weaponName.equals("ak-47") || weaponName.equals("ak47")) {
            material = WEAPON_MATERIALS.get("ak-47");
        } else if (weaponName.equals("ar-15") || weaponName.equals("ar15")) {
            material = WEAPON_MATERIALS.get("ar-15");
        } else if (weaponName.equals("spas-12") || weaponName.equals("spas12")) {
            material = WEAPON_MATERIALS.get("spas-12");
        } else if (weaponName.equals("barrett") || weaponName.equals("barret")) {
            material = WEAPON_MATERIALS.get("barrett");
        } else if (weaponName.equals("uzi")) {
            material = WEAPON_MATERIALS.get("uzi");
        } else if (weaponName.equals("dragunov")) {
            material = WEAPON_MATERIALS.get("dragunov");
        } else if (weaponName.equals("lança-chamas")) {
            material = WEAPON_MATERIALS.get("lança-chamas");
        } else if (weaponName.equals("3oitão")) {
            material = WEAPON_MATERIALS.get("3oitão");
        } else if (weaponName.equals("m4a1")) {
            material = WEAPON_MATERIALS.get("m4a1");
        } else if (weaponName.equals("rpg")) {
            material = WEAPON_MATERIALS.get("rpg");
        } else if (weaponName.equals("dmr")) {
            material = WEAPON_MATERIALS.get("dmr");
        }
        
        // Caso não encontre correspondência direta, usar um material padrão
        if (material == null) {
            LOGGER.warning("Material não encontrado para: " + weaponName + ", usando padrão");
            material = Material.IRON_HOE;
        }
        
        return material;
    }
}