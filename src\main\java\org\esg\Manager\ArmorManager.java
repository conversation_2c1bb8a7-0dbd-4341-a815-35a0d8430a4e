package org.esg.Manager;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.esg.enums.ArmorPiece;
import org.esg.enums.ArmorType;
import org.esg.models.Armor;
import org.esg.models.ArmorFactory;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Gerencia as armaduras dos jogadores.
 */
public class ArmorManager {

    private static final Map<UUID, Map<ArmorPiece, Armor>> playerArmorSets = new HashMap<>();

    /**
     * Equipa um conjunto completo de armadura em um jogador.
     *
     * @param player O jogador
     * @param armorType O tipo de armadura
     */
    public static void equipArmorSet(Player player, ArmorType armorType) {
        Map<ArmorPiece, Armor> armorSet = ArmorFactory.createArmorSet(armorType);
        equipArmorSet(player, armorSet);
    }

    /**
     * Equipa um conjunto completo de armadura em um jogador.
     *
     * @param player O jogador
     * @param armorSet O conjunto de armadura
     */
    public static void equipArmorSet(Player player, Map<ArmorPiece, Armor> armorSet) {
        // Armazenar o conjunto de armadura
        playerArmorSets.put(player.getUniqueId(), armorSet);

        // Equipar cada peça
        for (Armor armor : armorSet.values()) {
            armor.equip(player);
        }

        // Obter o tipo da armadura (assumindo que todas as peças são do mesmo tipo)
        ArmorType type = armorSet.values().iterator().next().getType();

        player.sendMessage("§aVocê equipou o conjunto completo de armadura " + type.getDisplayName() + "!");
    }

    /**
     * Equipa uma peça de armadura em um jogador.
     *
     * @param player O jogador
     * @param armor A peça de armadura
     */
    public static void equipArmorPiece(Player player, Armor armor) {
        // Obter ou criar o conjunto de armadura do jogador
        Map<ArmorPiece, Armor> armorSet = getPlayerArmorSet(player);
        if (armorSet == null) {
            armorSet = new EnumMap<>(ArmorPiece.class);
            playerArmorSets.put(player.getUniqueId(), armorSet);
        }

        // Adicionar a peça ao conjunto
        armorSet.put(armor.getPiece(), armor);

        // Equipar a peça
        armor.equip(player);

        player.sendMessage("§aVocê equipou " + armor.getName() + "!");
    }

    /**
     * Obtém o conjunto de armadura de um jogador.
     *
     * @param player O jogador
     * @return O conjunto de armadura do jogador, ou null se não tiver
     */
    public static Map<ArmorPiece, Armor> getPlayerArmorSet(Player player) {
        // Verificar primeiro no mapa
        Map<ArmorPiece, Armor> armorSet = playerArmorSets.get(player.getUniqueId());

        // Se não estiver no mapa, verificar os itens equipados
        if (armorSet == null) {
            armorSet = new EnumMap<>(ArmorPiece.class);
            boolean hasArmor = false;

            // Verificar cada peça de armadura
            ItemStack helmet = player.getInventory().getHelmet();
            if (isArmorItem(helmet)) {
                Armor armor = createArmorFromItem(helmet, ArmorPiece.HELMET);
                if (armor != null) {
                    armorSet.put(ArmorPiece.HELMET, armor);
                    hasArmor = true;
                }
            }

            ItemStack chestplate = player.getInventory().getChestplate();
            if (isArmorItem(chestplate)) {
                Armor armor = createArmorFromItem(chestplate, ArmorPiece.CHESTPLATE);
                if (armor != null) {
                    armorSet.put(ArmorPiece.CHESTPLATE, armor);
                    hasArmor = true;
                }
            }

            ItemStack leggings = player.getInventory().getLeggings();
            if (isArmorItem(leggings)) {
                Armor armor = createArmorFromItem(leggings, ArmorPiece.LEGGINGS);
                if (armor != null) {
                    armorSet.put(ArmorPiece.LEGGINGS, armor);
                    hasArmor = true;
                }
            }

            ItemStack boots = player.getInventory().getBoots();
            if (isArmorItem(boots)) {
                Armor armor = createArmorFromItem(boots, ArmorPiece.BOOTS);
                if (armor != null) {
                    armorSet.put(ArmorPiece.BOOTS, armor);
                    hasArmor = true;
                }
            }

            // Se encontrou alguma armadura, armazenar no mapa
            if (hasArmor) {
                playerArmorSets.put(player.getUniqueId(), armorSet);
            } else {
                armorSet = null;
            }
        }

        return armorSet;
    }

    /**
     * Verifica se um item é uma armadura personalizada.
     *
     * @param item O item
     * @return true se for uma armadura personalizada, false caso contrário
     */
    private static boolean isArmorItem(ItemStack item) {
        return item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName() && item.getItemMeta().hasLore();
    }

    /**
     * Cria uma armadura a partir de um item.
     *
     * @param item O item
     * @param piece A peça de armadura
     * @return A armadura, ou null se o item não for uma armadura válida
     */
    private static Armor createArmorFromItem(ItemStack item, ArmorPiece piece) {
        if (!isArmorItem(item)) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();
        String name = meta.getDisplayName().replace("§6", "");

        // Determinar o tipo de armadura
        ArmorType type = null;
        if (name.contains("Couro")) {
            type = ArmorType.LEATHER;
        } else if (name.contains("Kevlar")) {
            type = ArmorType.KEVLAR;
        }

        if (type != null) {
            return ArmorFactory.createArmorPiece(type, piece);
        }

        return null;
    }

    /**
     * Remove todas as armaduras de um jogador.
     *
     * @param player O jogador
     */
    public static void removeAllArmor(Player player) {
        playerArmorSets.remove(player.getUniqueId());
        player.getInventory().setHelmet(null);
        player.getInventory().setChestplate(null);
        player.getInventory().setLeggings(null);
        player.getInventory().setBoots(null);
        player.sendMessage("§cVocê removeu todas as suas armaduras!");
    }

    /**
     * Remove uma peça específica de armadura de um jogador.
     *
     * @param player O jogador
     * @param piece A peça de armadura
     */
    public static void removeArmorPiece(Player player, ArmorPiece piece) {
        Map<ArmorPiece, Armor> armorSet = getPlayerArmorSet(player);

        if (armorSet != null && armorSet.containsKey(piece)) {
            armorSet.remove(piece);

            switch (piece) {
                case HELMET:
                    player.getInventory().setHelmet(null);
                    break;
                case CHESTPLATE:
                    player.getInventory().setChestplate(null);
                    break;
                case LEGGINGS:
                    player.getInventory().setLeggings(null);
                    break;
                case BOOTS:
                    player.getInventory().setBoots(null);
                    break;
            }

            player.sendMessage("§cVocê removeu sua " + piece.getDisplayName().toLowerCase() + "!");
        }
    }

    /**
     * Calcula o dano reduzido pela armadura do jogador.
     *
     * @param player O jogador
     * @param damage O dano original
     * @return O dano reduzido
     */
    public static double calculateReducedDamage(Player player, double damage) {
        Map<ArmorPiece, Armor> armorSet = getPlayerArmorSet(player);

        if (armorSet == null || armorSet.isEmpty()) {
            return damage;
        }

        // Como as armaduras são inquebráveis, simplesmente calculamos o dano reduzido
        // sem verificar se alguma peça quebrou
        double reducedDamage = Armor.calculateReducedDamage(armorSet, damage);
        
        // Aplicar redução adicional baseada nos encantamentos de proteção contra projéteis
        // (assumimos que todos os danos de armas são considerados danos de projéteis)
        reducedDamage = Armor.calculateProjectileProtectionReduction(player, reducedDamage);
        
        return reducedDamage;
    }

    /**
     * Limpa os dados de armadura quando o jogador sai.
     *
     * @param player O jogador
     */
    public static void clearPlayerData(Player player) {
        playerArmorSets.remove(player.getUniqueId());
    }
}
