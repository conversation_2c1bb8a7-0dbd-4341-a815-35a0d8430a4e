package org.esg.listeners;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.esg.utils.StatsUtil;

/**
 * Listener para registrar estatísticas de armas.
 */
public class WeaponStatsListener implements Listener {
    
    /**
     * Registra a morte de uma entidade.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        
        // Registrar a morte da entidade
        StatsUtil.registerEntityDeath(entity);
    }
}
