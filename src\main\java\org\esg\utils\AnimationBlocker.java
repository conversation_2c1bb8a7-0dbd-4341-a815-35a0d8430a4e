package org.esg.utils;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Main;
import org.esg.models.Weapon;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Classe responsável por bloquear qualquer animação associada às armas.
 * Utiliza múltiplas abordagens para garantir que nenhuma animação seja exibida.
 */
public class AnimationBlocker implements Listener {
    
    private static final Map<UUID, Long> lastWeaponUseTime = new HashMap<>();
    private static final long ANIMATION_BLOCK_WINDOW_MS = 500; // Período em que bloquearemos qualquer animação
    
    /**
     * Registra esta classe como um listener no plugin.
     */
    public static void register(Plugin plugin) {
        plugin.getServer().getPluginManager().registerEvents(new AnimationBlocker(), plugin);
    }
    
    /**
     * Bloqueia qualquer evento de interação com enxadas na prioridade mais alta.
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        Player player = event.getPlayer();
        ItemStack itemInHand = player.getInventory().getItemInHand();
        
        if (isHoe(itemInHand) && isWeapon(itemInHand, player)) {
            // Cancelar evento e todas as suas ações
            event.setCancelled(true);
            event.setUseItemInHand(Event.Result.DENY);
            event.setUseInteractedBlock(Event.Result.DENY);
            
            // Registrar o momento do uso para bloquear animações subsequentes
            lastWeaponUseTime.put(player.getUniqueId(), System.currentTimeMillis());
        }
    }
    
    /**
     * Bloqueia qualquer tipo de animação do jogador logo após usar uma arma.
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerAnimation(PlayerAnimationEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        // Verificar se o jogador usou uma arma recentemente
        Long lastUse = lastWeaponUseTime.get(playerId);
        if (lastUse != null && (System.currentTimeMillis() - lastUse) < ANIMATION_BLOCK_WINDOW_MS) {
            event.setCancelled(true);
            return;
        }
        
        // Verificar se o jogador está segurando uma arma
        ItemStack itemInHand = player.getInventory().getItemInHand();
        if (isHoe(itemInHand) && isWeapon(itemInHand, player)) {
            event.setCancelled(true);
        }
    }
    
    /**
     * Registra o uso de uma arma para bloquear animações subsequentes.
     */
    public static void registerWeaponUse(Player player) {
        lastWeaponUseTime.put(player.getUniqueId(), System.currentTimeMillis());
        
        // Agendar um "reset" do cliente para garantir que a animação seja interrompida
        new BukkitRunnable() {
            @Override
            public void run() {
                // Verificar se o jogador ainda está online
                if (player.isOnline()) {
                    // O truque é fazer com que o cliente receba atualizações que forçam
                    // o reset de qualquer animação em curso
                    player.updateInventory();
                }
            }
        }.runTaskLater(Main.getPlugin(), 1L);
    }
    
    /**
     * Verifica se um item é uma enxada.
     */
    private boolean isHoe(ItemStack item) {
        return item != null && (
            item.getType() == Material.WOOD_HOE ||
            item.getType() == Material.STONE_HOE ||
            item.getType() == Material.IRON_HOE ||
            item.getType() == Material.GOLD_HOE ||
            item.getType() == Material.DIAMOND_HOE
        );
    }
    
    /**
     * Verifica se um item é uma arma.
     */
    private boolean isWeapon(ItemStack item, Player player) {
        return WeaponUtils.getWeaponFromItem(item, player) != null;
    }
} 