package org.esg.models;

import org.esg.enums.ArmorPiece;
import org.esg.enums.ArmorType;

import java.util.EnumMap;
import java.util.Map;

/**
 * Fábrica para criar instâncias de armaduras.
 */
public class ArmorFactory {

    /**
     * Cria uma peça de armadura de couro (Leather).
     *
     * @param piece A peça de armadura
     * @return A peça de armadura de couro
     */
    public static Armor createLeatherArmorPiece(ArmorPiece piece) {
        String name = piece.getDisplayName() + " de Couro";
        return new Armor(ArmorType.LEATHER, piece, name, 100);
    }

    /**
     * Cria uma peça de armadura Kevlar.
     *
     * @param piece A peça de armadura
     * @return A peça de armadura Kevlar
     */
    public static Armor createKevlarArmorPiece(ArmorPiece piece) {
        String name = piece.getDisplayName() + " Kevlar";
        return new Armor(ArmorType.KEVLAR, piece, name, 200);
    }

    /**
     * Cria uma peça de armadura pelo tipo.
     *
     * @param type O tipo de armadura
     * @param piece A peça de armadura
     * @return A peça de armadura criada
     */
    public static Armor createArmorPiece(ArmorType type, ArmorPiece piece) {
        switch (type) {
            case LEATHER:
                return createLeatherArmorPiece(piece);
            case KEVLAR:
                return createKevlarArmorPiece(piece);
            default:
                return createLeatherArmorPiece(piece);
        }
    }

    /**
     * Cria um conjunto completo de armadura pelo tipo.
     *
     * @param type O tipo de armadura
     * @return Um mapa com todas as peças de armadura
     */
    public static Map<ArmorPiece, Armor> createArmorSet(ArmorType type) {
        Map<ArmorPiece, Armor> armorSet = new EnumMap<>(ArmorPiece.class);

        for (ArmorPiece piece : ArmorPiece.values()) {
            armorSet.put(piece, createArmorPiece(type, piece));
        }

        return armorSet;
    }

    /**
     * Cria um conjunto completo de armadura pelo nome.
     *
     * @param name O nome do tipo de armadura
     * @return Um mapa com todas as peças de armadura, ou null se o nome for inválido
     */
    public static Map<ArmorPiece, Armor> createArmorSet(String name) {
        ArmorType type = getArmorTypeByName(name);

        if (type != null) {
            return createArmorSet(type);
        }

        return null;
    }

    /**
     * Obtém o tipo de armadura pelo nome.
     *
     * @param name O nome do tipo de armadura
     * @return O tipo de armadura, ou null se o nome for inválido
     */
    public static ArmorType getArmorTypeByName(String name) {
        if (name.equalsIgnoreCase("leather") || name.equalsIgnoreCase("couro")) {
            return ArmorType.LEATHER;
        } else if (name.equalsIgnoreCase("kevlar") || name.equalsIgnoreCase("diamante")) {
            return ArmorType.KEVLAR;
        }

        return null;
    }
}
