package org.esg.enums;

import org.bukkit.Material;

/**
 * Representa as peças de armadura disponíveis.
 */
public enum ArmorPiece {
    HELMET("Capacete", 0.2),
    CHESTPLATE("Peitoral", 0.4),
    LEGGINGS("Calças", 0.3),
    BOOTS("Botas", 0.1);
    
    private final String displayName;
    private final double protectionRatio;
    
    ArmorPiece(String displayName, double protectionRatio) {
        this.displayName = displayName;
        this.protectionRatio = protectionRatio;
    }
    
    /**
     * Obtém o nome de exibição da peça de armadura.
     * 
     * @return O nome de exibição
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Obtém a proporção de proteção desta peça em relação ao conjunto completo.
     * 
     * @return A proporção de proteção (0.0 a 1.0)
     */
    public double getProtectionRatio() {
        return protectionRatio;
    }
    
    /**
     * Obtém o material correspondente para esta peça de armadura de couro.
     * Modificado para retornar materiais de ferro mantendo o nome "couro".
     * 
     * @return O material da peça de armadura de ferro (visual) com nome de couro
     */
    public Material getLeatherMaterial() {
        switch (this) {
            case HELMET:
                return Material.IRON_HELMET;
            case CHESTPLATE:
                return Material.IRON_CHESTPLATE;
            case LEGGINGS:
                return Material.IRON_LEGGINGS;
            case BOOTS:
                return Material.IRON_BOOTS;
            default:
                return Material.IRON_CHESTPLATE;
        }
    }
    
    /**
     * Obtém o material correspondente para esta peça de armadura Kevlar.
     * 
     * @return O material da peça de armadura Kevlar
     */
    public Material getKevlarMaterial() {
        switch (this) {
            case HELMET:
                return Material.DIAMOND_HELMET;
            case CHESTPLATE:
                return Material.DIAMOND_CHESTPLATE;
            case LEGGINGS:
                return Material.DIAMOND_LEGGINGS;
            case BOOTS:
                return Material.DIAMOND_BOOTS;
            default:
                return Material.DIAMOND_CHESTPLATE;
        }
    }
    
    /**
     * Obtém o material correspondente para esta peça de armadura com base no tipo.
     * 
     * @param type O tipo de armadura
     * @return O material da peça de armadura
     */
    public Material getMaterial(ArmorType type) {
        switch (type) {
            case LEATHER:
                return getLeatherMaterial();
            case KEVLAR:
                return getKevlarMaterial();
            default:
                return getLeatherMaterial();
        }
    }
}
