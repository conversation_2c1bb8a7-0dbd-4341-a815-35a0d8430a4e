package org.esg.models.weapons;

import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;

/**
 * Três O<PERSON>ão - Uma pistola de tambor de dano médio
 * 
 * Características:
 * - <PERSON><PERSON> médio-alto para uma pistola
 * - <PERSON>car<PERSON> mais lenta que pistolas normais
 * - Precisão média
 * - Taxa de disparo média
 * - Efeito de knockback aumentado
 */
public class TresOitao extends Weapon {
    
    public TresOitao() {
        super(
            "Três Oitão",      // nome 
            WeaponType.PISTOL, // tipo
            AmmoType._9MM,     // tipo de munição
            8.0,               // dano (mais alto que pistolas normais)
            40.0,              // alcance em blocos
            0.75,              // precisão (0.0 a 1.0)
            3.0,               // taxa de disparo (tiros por segundo)
            150.0,             // velocidade do projétil
            6,                 // capacidade máxima
            6,                 // munição inicial
            3,                 // tempo de recarga (segundos)
            1,                 // número de projéteis por tiro
            1.5                // multiplicador de headshot
        );
    }
    
    @Override
    public void startFiring(org.bukkit.entity.Player player) {
        // O Três Oitão é uma arma que dispara mais lentamente, mas com mais impacto
        super.startFiring(player);
        
        // Reproduzir um som mais forte ao disparar
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.BLAZE_HIT, 1.0f, 0.6f);
    }
} 