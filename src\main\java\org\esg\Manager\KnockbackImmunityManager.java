package org.esg.Manager;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Main;
import org.esg.enums.WeaponType;

import java.io.File;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Manages knockback immunity for players to prevent knockback spam from weapon shots.
 * Players become immune to knockback for a configurable duration after being hit.
 */
public class KnockbackImmunityManager {

    private static final Logger LOGGER = Logger.getLogger(KnockbackImmunityManager.class.getName());

    // Thread-safe map to track player immunity
    private static final Map<UUID, Long> immunityMap = new ConcurrentHashMap<>();

    // Map to store weapon-specific immunity durations
    private static final Map<String, Long> weaponSpecificDurations = new ConcurrentHashMap<>();

    // Default immunity durations in milliseconds - VALORES FIXOS NO CÓDIGO
    private static long defaultImmunityDuration = 400; // 0.4 seconds
    private static long automaticWeaponImmunityDuration = 500; // 0.5 seconds for automatic weapons (AK-47, UZI, etc)
    private static long singleShotWeaponImmunityDuration = 400; // 0.4 seconds for single-shot weapons (Shotgun, Pistol)
    private static long sniperWeaponImmunityDuration = 600; // 0.6 seconds for snipers (Barrett, Dragunov)

    // Configuration
    private static FileConfiguration weaponsConfig;
    private static boolean systemEnabled = true;

    /**
     * Initializes the knockback immunity system.
     * Starts the cleanup task to remove expired immunity entries.
     */
    public static void initialize() {
        LOGGER.info("Initializing KnockbackImmunityManager...");

        // Load configuration values
        loadConfiguration();

        // Setup weapon-specific durations directly in code
        setupWeaponSpecificDurations();

        // Start cleanup task every 5 seconds to prevent memory leaks
        new BukkitRunnable() {
            @Override
            public void run() {
                cleanupExpiredImmunity();
            }
        }.runTaskTimer(Main.getPlugin(), 100L, 100L); // Every 5 seconds

        LOGGER.info("KnockbackImmunityManager initialized successfully!");
        LOGGER.info("System enabled: " + systemEnabled);
        LOGGER.info("Default duration: " + defaultImmunityDuration + "ms");
        LOGGER.info("Automatic weapons: " + automaticWeaponImmunityDuration + "ms");
        LOGGER.info("Single-shot weapons: " + singleShotWeaponImmunityDuration + "ms");
        LOGGER.info("Sniper weapons: " + sniperWeaponImmunityDuration + "ms");
        LOGGER.info("Weapon-specific overrides: " + weaponSpecificDurations.size());
    }

    /**
     * Setup weapon-specific immunity durations directly in code.
     */
    private static void setupWeaponSpecificDurations() {
        // Armas automáticas - intervalos específicos
        weaponSpecificDurations.put("AK-47", 500L);      // 0.5s
        weaponSpecificDurations.put("UZI", 500L);        // 0.5s
        weaponSpecificDurations.put("AR-15", 250L);      // 0.25s - Intervalo menor para efeito gradual mais suave
        weaponSpecificDurations.put("M4A1", 500L);       // 0.5s

        // Armas de tiro único - intervalos médios
        weaponSpecificDurations.put("Spas-12", 450L);    // 0.45s
        weaponSpecificDurations.put("3oitão", 350L);     // 0.35s

        // Snipers - intervalos maiores
        weaponSpecificDurations.put("Barrett", 700L);    // 0.7s
        weaponSpecificDurations.put("Dragunov", 650L);   // 0.65s
        weaponSpecificDurations.put("DMR", 500L);        // 0.5s

        // Armas especiais
        weaponSpecificDurations.put("RPG", 1000L);       // 1.0s
        weaponSpecificDurations.put("Lança-Chamas", 300L); // 0.3s

        LOGGER.info("Loaded " + weaponSpecificDurations.size() + " weapon-specific immunity durations");
    }

    /**
     * Loads configuration values from both config.yml and weapons.yml.
     */
    private static void loadConfiguration() {
        Main plugin = Main.getPlugin();
        if (plugin == null) {
            LOGGER.warning("Plugin instance is null, using default values");
            return;
        }

        LOGGER.info("Loading knockback immunity configuration...");

        // Load from main config.yml
        systemEnabled = plugin.getConfig().getBoolean("knockback-immunity.enabled", true);
        defaultImmunityDuration = plugin.getConfig().getLong("knockback-immunity.default-duration", 500);
        automaticWeaponImmunityDuration = plugin.getConfig().getLong("knockback-immunity.automatic-weapons", 300);
        singleShotWeaponImmunityDuration = plugin.getConfig().getLong("knockback-immunity.single-shot-weapons", 500);
        sniperWeaponImmunityDuration = plugin.getConfig().getLong("knockback-immunity.sniper-weapons", 800);

        LOGGER.info("Loaded config values - enabled: " + systemEnabled +
                   ", default: " + defaultImmunityDuration +
                   ", auto: " + automaticWeaponImmunityDuration +
                   ", single: " + singleShotWeaponImmunityDuration +
                   ", sniper: " + sniperWeaponImmunityDuration);

        // Load weapons.yml configuration
        loadWeaponsConfig();
    }

    /**
     * Loads weapon-specific configuration from weapons.yml.
     */
    private static void loadWeaponsConfig() {
        Main plugin = Main.getPlugin();
        File weaponsFile = new File(plugin.getDataFolder(), "weapons.yml");

        if (!weaponsFile.exists()) {
            // Save default weapons.yml from resources
            plugin.saveResource("weapons.yml", false);
        }

        weaponsConfig = YamlConfiguration.loadConfiguration(weaponsFile);

        // Load individual weapon immunity durations
        if (weaponsConfig.contains("weapons.individual")) {
            for (String weaponName : weaponsConfig.getConfigurationSection("weapons.individual").getKeys(false)) {
                long duration = weaponsConfig.getLong("weapons.individual." + weaponName + ".knockback-immunity", -1);
                if (duration > 0) {
                    weaponSpecificDurations.put(weaponName, duration);
                    LOGGER.info("Loaded custom knockback immunity for " + weaponName + ": " + duration + "ms");
                }
            }
        }
    }

    /**
     * Checks if a player is currently immune to knockback.
     *
     * @param player The player to check
     * @return true if the player is immune, false otherwise
     */
    public static boolean isImmune(Player player) {
        if (player == null || !systemEnabled) {
            return false;
        }

        UUID playerId = player.getUniqueId();
        Long immunityEndTime = immunityMap.get(playerId);

        if (immunityEndTime == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime >= immunityEndTime) {
            // Immunity has expired, remove from map
            immunityMap.remove(playerId);
            LOGGER.info("Knockback immunity expired for " + player.getName());
            return false;
        }

        // Player is immune
        return true;
    }

    /**
     * Applies knockback immunity to a player based on the weapon type.
     *
     * @param player The player to apply immunity to
     * @param weaponType The type of weapon that caused the hit
     */
    public static void applyImmunity(Player player, WeaponType weaponType) {
        if (player == null || weaponType == null || !systemEnabled) return;

        long duration = getImmunityDuration(weaponType);
        long immunityEndTime = System.currentTimeMillis() + duration;

        immunityMap.put(player.getUniqueId(), immunityEndTime);

        // Debug logging
        LOGGER.info("Applied knockback immunity to " + player.getName() +
                   " for " + duration + "ms (weapon: " + weaponType + ")");
    }

    /**
     * Applies knockback immunity to a player based on the specific weapon name.
     * This method checks for weapon-specific overrides first.
     *
     * @param player The player to apply immunity to
     * @param weaponName The name of the weapon that caused the hit
     * @param weaponType The type of weapon (fallback if no specific override)
     */
    public static void applyImmunity(Player player, String weaponName, WeaponType weaponType) {
        if (player == null || !systemEnabled) return;

        long duration = getImmunityDuration(weaponName, weaponType);
        long immunityEndTime = System.currentTimeMillis() + duration;

        immunityMap.put(player.getUniqueId(), immunityEndTime);

        // Debug logging
        LOGGER.info("Applied knockback immunity to " + player.getName() +
                   " for " + duration + "ms (weapon: " + weaponName + ", type: " + weaponType + ")");
    }

    /**
     * Gets the immunity duration for a specific weapon type.
     *
     * @param weaponType The weapon type
     * @return The immunity duration in milliseconds
     */
    private static long getImmunityDuration(WeaponType weaponType) {
        switch (weaponType) {
            case SMG:
            case RIFLE:
                // Automatic weapons get shorter immunity to prevent spam
                return automaticWeaponImmunityDuration;
            case SNIPER:
                // Snipers get longer immunity due to high damage
                return sniperWeaponImmunityDuration;
            case SHOTGUN:
            case PISTOL:
            default:
                // Single-shot weapons get standard immunity
                return singleShotWeaponImmunityDuration;
        }
    }

    /**
     * Gets the immunity duration for a specific weapon, checking for weapon-specific overrides first.
     *
     * @param weaponName The name of the weapon
     * @param weaponType The weapon type (fallback)
     * @return The immunity duration in milliseconds
     */
    private static long getImmunityDuration(String weaponName, WeaponType weaponType) {
        // Check for weapon-specific override first
        if (weaponName != null && weaponSpecificDurations.containsKey(weaponName)) {
            return weaponSpecificDurations.get(weaponName);
        }

        // Fall back to weapon type duration
        return getImmunityDuration(weaponType);
    }

    /**
     * Manually removes immunity from a player.
     *
     * @param player The player to remove immunity from
     */
    public static void removeImmunity(Player player) {
        if (player != null) {
            immunityMap.remove(player.getUniqueId());
        }
    }

    /**
     * Cleans up expired immunity entries to prevent memory leaks.
     */
    private static void cleanupExpiredImmunity() {
        long currentTime = System.currentTimeMillis();
        int initialSize = immunityMap.size();

        immunityMap.entrySet().removeIf(entry -> currentTime >= entry.getValue());

        int removedCount = initialSize - immunityMap.size();
        if (removedCount > 0) {
            LOGGER.fine("Cleaned up " + removedCount + " expired knockback immunity entries");
        }
    }

    /**
     * Gets the current number of players with active immunity.
     * Used for monitoring and debugging.
     *
     * @return The number of immune players
     */
    public static int getActiveImmunityCount() {
        return immunityMap.size();
    }

    /**
     * Gets the remaining immunity time for a player in milliseconds.
     *
     * @param player The player to check
     * @return Remaining immunity time in milliseconds, or 0 if not immune
     */
    public static long getRemainingImmunityTime(Player player) {
        if (player == null || !systemEnabled) return 0;

        UUID playerId = player.getUniqueId();
        Long immunityEndTime = immunityMap.get(playerId);

        if (immunityEndTime == null) {
            return 0;
        }

        long currentTime = System.currentTimeMillis();
        long remaining = immunityEndTime - currentTime;

        return Math.max(0, remaining);
    }

    /**
     * Reloads configuration from files.
     * Useful for runtime configuration updates.
     */
    public static void reloadConfiguration() {
        weaponSpecificDurations.clear();
        loadConfiguration();
        LOGGER.info("KnockbackImmunityManager configuration reloaded");
    }

    /**
     * Checks if the knockback immunity system is enabled.
     *
     * @return true if the system is enabled, false otherwise
     */
    public static boolean isSystemEnabled() {
        return systemEnabled;
    }

    /**
     * Enables or disables the knockback immunity system.
     *
     * @param enabled Whether to enable the system
     */
    public static void setSystemEnabled(boolean enabled) {
        systemEnabled = enabled;
        if (!enabled) {
            // Clear all immunity when disabling
            immunityMap.clear();
        }
        LOGGER.info("KnockbackImmunityManager " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Clears all immunity data. Used when the plugin is disabled.
     */
    public static void shutdown() {
        immunityMap.clear();
        weaponSpecificDurations.clear();
        LOGGER.info("KnockbackImmunityManager shutdown - cleared all immunity data");
    }
}
