package org.esg.listeners;

import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.inventory.ItemStack;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.utils.MessageHandler;
import org.esg.utils.WeaponUtils;

import java.util.UUID;

/**
 * Listener para gerenciar o delay ao trocar para armas poderosas.
 * Impede que o jogador atire imediatamente ao trocar para uma sniper.
 */
public class WeaponSwitchListener implements Listener {
    private static final int WEAPON_READY_DELAY = 15; // 0.75 segundos (15 ticks)

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        UUID playerUUID = player.getUniqueId();

        // Limpar estado de disparo e registrar troca de arma
        Weapon.stopAllFiring(player);
        Weapon.registerWeaponSwitch(player);

        // Salvar estado da arma anterior
        ItemStack previousItem = player.getInventory().getItem(event.getPreviousSlot());
        if (previousItem != null) {
            Weapon prevWeapon = WeaponUtils.getWeaponFromItem(previousItem, player);
            if (prevWeapon != null) {
                // Atualizar estado da arma no inventário
                WeaponUtils.updateWeaponInSlot(player, event.getPreviousSlot(), prevWeapon);
            }
        }

        // Verificar se o novo item é uma arma
        ItemStack newItem = player.getInventory().getItem(event.getNewSlot());
        if (newItem == null) return;

        // Verificar se é uma arma válida
        boolean isWeapon = false;
        for (Material material : new Material[]{Material.IRON_HOE, Material.STONE_HOE, Material.WOOD_HOE, Material.DIAMOND_HOE, Material.GOLD_HOE}) {
            if (newItem.getType() == material) {
                isWeapon = true;
                break;
            }
        }

        if (!isWeapon) return;

        Weapon weapon = WeaponUtils.getWeaponFromItem(newItem, player);
        if (weapon == null) return;

        // Old weapon ready delay system removed - using KnockbackImmunityManager instead
    }
}
