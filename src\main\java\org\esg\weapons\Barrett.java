package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;
import java.util.logging.Logger;

public class Barrett extends Weapon {
    private static final Logger LOGGER = Logger.getLogger(Barrett.class.getName());

    public Barrett() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProj<PERSON>is, MultiplicadorHeadshot
        super("Barrett", WeaponType.SNIPER, AmmoType._50CAL, 15, 200, 1, 0.5, 150, 5, 5, 4, 1, 1.5);

        // A <PERSON> tem:
        // - Dano base extremamente alto (15 vs 2 da AK47)
        // - Multiplicador de distância: 0.5x a curta distância, 1.6x a longa distância
        // - <PERSON><PERSON> mínimo a curta distância: 7.5 (15 * 0.5)
        // - <PERSON><PERSON> máximo a longa distância: 24.0 (15 * 1.6)
        // - Alcance muito maior (200 vs 80 da AK47)
        // - Precisão extremamente alta (1.0 vs 0.25 da AK47) - Isso causa um espalhamento mínimo
        // - Velocidade de tiro muito baixa (0.5 vs 4.5 da AK47) - Aproximadamente 1 tiro a cada 2 segundos
        // - Velocidade de projétil muito alta (150 vs 90 da AK47)
        // - 1 projétil por tiro
        // - Multiplicador de headshot base: 1.5x (vs 3.0x padrão para snipers)
        // - Multiplicador de headshot varia com a distância: 1.2x a curta distância (1.5 * 0.8), 2.7x a longa distância (1.5 * 1.8)
        // - Detecção de headshot mais fácil e menos restrita
        // - Sniper de alto dano, ideal para tiros de longa distância
        // - Tempo de recarga maior (4 vs 3 da AK47)
        // - Capacidade de munição menor (5 vs 32 da AK47)
        
        LOGGER.info("Barrett inicializada com sucesso - Nome: " + getName() + 
                   ", Tipo: " + getType() + 
                   ", Munição: " + getCurrentAmmo() + "/" + getMaxAmmo());
    }
    
    @Override
    public String getName() {
        return "Barrett";
    }
    
    @Override
    public WeaponType getType() {
        return WeaponType.SNIPER;
    }
    
    @Override
    public AmmoType getAmmoType() {
        return AmmoType._50CAL;
    }
}
