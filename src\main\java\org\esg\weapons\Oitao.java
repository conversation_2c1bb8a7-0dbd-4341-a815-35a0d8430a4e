package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;

public class <PERSON>itao extends Weapon {

    public Oitao() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProj<PERSON>teis, MultiplicadorHeadshot
        super("3oitão", WeaponType.PISTOL, AmmoType._50CAL, 8, 40, 0.7, 2.0, 100, 6, 6, 2, 1, 2.5);

        // O 3oitão tem:
        // - Dano base alto para uma pistola (8)
        // - Multiplicador de distância: 0.8x a curta distância, 1.2x a longa distância
        // - <PERSON><PERSON> mínimo a curta distância: 6.4 (8 * 0.8)
        // - Dano máximo a longa distância: 9.6 (8 * 1.2)
        // - <PERSON><PERSON><PERSON> médio (40)
        // - <PERSON><PERSON><PERSON> boa (0.7)
        // - Velocidade de tiro moderada (2.0 tiros por segundo)
        // - Velocidade de projétil alta (100)
        // - Capacidade de munição baixa (6)
        // - Tempo de recarga mais lento (2 segundos)
        // - 1 projétil por tiro
        // - Multiplicador de headshot alto (2.5x)
        // - Uma pistola potente, inspirada nos revólveres de alto calibre
    }
} 