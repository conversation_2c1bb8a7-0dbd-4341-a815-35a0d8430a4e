import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.block.Chest;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Exemplo de como integrar com a ArmorAPI do plugin esgotoserver.
 * Este plugin demonstra como acessar e utilizar a API de armaduras.
 */
public class ArmorAPIExample extends JavaPlugin implements Listener {

    private boolean armorAPIAvailable = false;
    private Class<?> armorAPIClass;
    private Random random = new Random();

    // Configuração de probabilidades de spawn para cada tipo de armadura
    private Map<String, Double> armorSpawnChances = new HashMap<>();

    @Override
    public void onEnable() {
        // Verificar se o plugin de armaduras está disponível
        if (checkArmorPlugin()) {
            getLogger().info("Plugin de armaduras encontrado! Integrando...");
            setupArmorSpawnChances();
        } else {
            getLogger().warning("Plugin de armaduras não encontrado! Funcionalidades de armaduras estarão desativadas.");
        }
        
        // Registrar eventos
        getServer().getPluginManager().registerEvents(this, this);
    }

    /**
     * Verifica se o plugin de armaduras está disponível e configura a API.
     */
    private boolean checkArmorPlugin() {
        Plugin armorPlugin = Bukkit.getPluginManager().getPlugin("esgotoserver");
        if (armorPlugin == null || !armorPlugin.isEnabled()) {
            return false;
        }

        try {
            // Obter a classe da API através de reflexão
            Class<?> mainClass = Class.forName("org.esg.Main");
            Method getAPIMethod = mainClass.getMethod("getArmorAPI");
            armorAPIClass = (Class<?>) getAPIMethod.invoke(null);

            // Se chegou até aqui, a API está disponível
            armorAPIAvailable = true;
            return true;
        } catch (Exception e) {
            getLogger().warning("Erro ao acessar a API de armaduras: " + e.getMessage());
            return false;
        }
    }

    /**
     * Configura as chances de spawn para cada tipo de armadura.
     */
    private void setupArmorSpawnChances() {
        // Definir chances de spawn para cada tipo de armadura
        armorSpawnChances.put("LEATHER", 0.7); // 70% de chance para armadura de couro
        armorSpawnChances.put("KEVLAR", 0.3); // 30% de chance para armadura Kevlar
    }

    /**
     * Manipula o evento de interação do jogador com baús.
     * Quando um jogador abre um baú, há uma chance de gerar armaduras dentro dele.
     */
    @EventHandler
    public void onChestOpen(PlayerInteractEvent event) {
        if (!armorAPIAvailable) return;
        
        Player player = event.getPlayer();
        
        // Verificar se o jogador está interagindo com um baú
        if (event.getClickedBlock() != null && event.getClickedBlock().getType() == Material.CHEST) {
            Chest chest = (Chest) event.getClickedBlock().getState();
            
            // Verificar se o baú está vazio (para não adicionar itens repetidamente)
            if (isEmpty(chest.getInventory())) {
                // Gerar armaduras no baú
                generateArmorInChest(chest);
                player.sendMessage("§aVocê encontrou um baú com armaduras!");
            }
        }
    }

    /**
     * Verifica se um inventário está vazio.
     */
    private boolean isEmpty(Inventory inventory) {
        for (ItemStack item : inventory.getContents()) {
            if (item != null && item.getType() != Material.AIR) {
                return false;
            }
        }
        return true;
    }

    /**
     * Gera armaduras aleatórias dentro de um baú.
     */
    private void generateArmorInChest(Chest chest) {
        try {
            // Obter tipos de armaduras disponíveis
            Method getAvailableArmorTypesMethod = armorAPIClass.getMethod("getAvailableArmorTypes");
            List<String> armorTypes = (List<String>) getAvailableArmorTypesMethod.invoke(null);
            
            // Obter peças de armaduras disponíveis
            Method getAvailableArmorPiecesMethod = armorAPIClass.getMethod("getAvailableArmorPieces");
            List<String> armorPieces = (List<String>) getAvailableArmorPiecesMethod.invoke(null);
            
            // Método para converter armadura em ItemStack
            Method toItemStackMethod = armorAPIClass.getMethod("toItemStack", String.class, String.class);
            
            // Gerar de 1 a 3 peças de armadura
            int numArmors = random.nextInt(3) + 1;
            
            for (int i = 0; i < numArmors; i++) {
                // Escolher tipo de armadura com base nas probabilidades
                String armorType = chooseArmorType();
                
                // Escolher peça de armadura aleatória
                String armorPiece = armorPieces.get(random.nextInt(armorPieces.size()));
                
                // Criar o ItemStack da armadura
                ItemStack armorItem = (ItemStack) toItemStackMethod.invoke(null, armorType, armorPiece);
                
                if (armorItem != null) {
                    // Adicionar ao baú em um slot aleatório
                    chest.getInventory().setItem(random.nextInt(27), armorItem);
                }
            }
        } catch (Exception e) {
            getLogger().warning("Erro ao gerar armaduras: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Escolhe um tipo de armadura com base nas probabilidades configuradas.
     */
    private String chooseArmorType() {
        double roll = random.nextDouble();
        double cumulativeProbability = 0.0;
        
        for (Map.Entry<String, Double> entry : armorSpawnChances.entrySet()) {
            cumulativeProbability += entry.getValue();
            if (roll <= cumulativeProbability) {
                return entry.getKey();
            }
        }
        
        // Fallback para LEATHER se algo der errado
        return "LEATHER";
    }

    /**
     * Exemplo de como verificar o dano reduzido pela armadura de um jogador.
     */
    public double getReducedDamage(Player player, double damage) {
        if (!armorAPIAvailable) return damage;
        
        try {
            Method calculateReducedDamageMethod = armorAPIClass.getMethod("calculateReducedDamage", Player.class, double.class);
            return (double) calculateReducedDamageMethod.invoke(null, player, damage);
        } catch (Exception e) {
            getLogger().warning("Erro ao calcular dano reduzido: " + e.getMessage());
            return damage;
        }
    }

    /**
     * Exemplo de como obter informações sobre a armadura de um jogador.
     */
    public void printPlayerArmorInfo(Player player) {
        if (!armorAPIAvailable) return;
        
        try {
            Method getPlayerArmorSetMethod = armorAPIClass.getMethod("getPlayerArmorSet", Player.class);
            Map<String, Object> armorSet = (Map<String, Object>) getPlayerArmorSetMethod.invoke(null, player);
            
            if (armorSet == null || armorSet.isEmpty()) {
                player.sendMessage("§cVocê não está usando nenhuma armadura!");
                return;
            }
            
            player.sendMessage("§6=== Sua Armadura ===");
            for (Map.Entry<String, Object> entry : armorSet.entrySet()) {
                String pieceName = entry.getKey();
                Object armorObj = entry.getValue();
                
                // Obter o nome da armadura usando reflexão
                Method getNameMethod = armorObj.getClass().getMethod("getName");
                String armorName = (String) getNameMethod.invoke(armorObj);
                
                // Obter a redução de dano usando reflexão
                Method getDamageReductionMethod = armorObj.getClass().getMethod("getDamageReduction");
                double damageReduction = (double) getDamageReductionMethod.invoke(armorObj);
                
                player.sendMessage(String.format("§7%s: §a%s §7(Proteção: §a%.0f%%§7)", 
                        pieceName, armorName, damageReduction * 100));
            }
        } catch (Exception e) {
            getLogger().warning("Erro ao obter informações da armadura: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
