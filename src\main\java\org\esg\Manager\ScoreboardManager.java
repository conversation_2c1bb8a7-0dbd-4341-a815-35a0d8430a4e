package org.esg.Manager;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.esg.Main;
import org.esg.stats.PlayerStats;
import org.esg.stats.StatsManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Gerencia os scoreboards dos jogadores.
 */
public class ScoreboardManager {
    private static final Map<UUID, Scoreboard> playerScoreboards = new HashMap<>();
    private static final String OBJECTIVE_NAME = "stats";
    private static final String DISPLAY_NAME = ChatColor.GOLD + "Estatísticas";
    
    /**
     * Cria um scoreboard para um jogador.
     * 
     * @param player O jogador
     */
    public static void createScoreboard(Player player) {
        org.bukkit.scoreboard.ScoreboardManager manager = Bukkit.getScoreboardManager();
        Scoreboard scoreboard = manager.getNewScoreboard();
        
        Objective objective = scoreboard.registerNewObjective(OBJECTIVE_NAME, "dummy");
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);
        objective.setDisplayName(DISPLAY_NAME);
        
        updateScoreboard(player, scoreboard);
        
        player.setScoreboard(scoreboard);
        playerScoreboards.put(player.getUniqueId(), scoreboard);
    }
    
    /**
     * Atualiza o scoreboard de um jogador.
     * 
     * @param player O jogador
     */
    public static void updateScoreboard(Player player) {
        Scoreboard scoreboard = playerScoreboards.get(player.getUniqueId());
        
        if (scoreboard == null) {
            createScoreboard(player);
            return;
        }
        
        updateScoreboard(player, scoreboard);
    }
    
    /**
     * Atualiza o scoreboard de um jogador com um scoreboard específico.
     * 
     * @param player O jogador
     * @param scoreboard O scoreboard
     */
    private static void updateScoreboard(Player player, Scoreboard scoreboard) {
        Objective objective = scoreboard.getObjective(OBJECTIVE_NAME);
        
        if (objective == null) {
            objective = scoreboard.registerNewObjective(OBJECTIVE_NAME, "dummy");
            objective.setDisplaySlot(DisplaySlot.SIDEBAR);
            objective.setDisplayName(DISPLAY_NAME);
        }
        
        // Limpar scores antigos
        for (String entry : scoreboard.getEntries()) {
            scoreboard.resetScores(entry);
        }
        
        // Obter estatísticas do jogador
        PlayerStats stats = StatsManager.getPlayerStats(player);
        
        // Adicionar estatísticas ao scoreboard
        Score killsScore = objective.getScore(ChatColor.GREEN + "Kills: " + ChatColor.WHITE + stats.getKills());
        killsScore.setScore(10);
        
        Score deathsScore = objective.getScore(ChatColor.RED + "Mortes: " + ChatColor.WHITE + stats.getDeaths());
        deathsScore.setScore(9);
        
        Score kdScore = objective.getScore(ChatColor.GOLD + "K/D: " + ChatColor.WHITE + String.format("%.2f", stats.getKDRatio()));
        kdScore.setScore(8);
        
        Score headshotsScore = objective.getScore(ChatColor.LIGHT_PURPLE + "Headshots: " + ChatColor.WHITE + stats.getHeadshots());
        headshotsScore.setScore(7);
        
        Score accuracyScore = objective.getScore(ChatColor.AQUA + "Precisão: " + ChatColor.WHITE + String.format("%.1f%%", stats.getAccuracy()));
        accuracyScore.setScore(6);
        
        // Verificar se o jogador está em PvP tag
        if (PvPTagManager.isTagged(player)) {
            int remainingSeconds = PvPTagManager.getRemainingSeconds(player);
            Score pvpTagScore = objective.getScore(ChatColor.RED + "PvP Tag: " + ChatColor.WHITE + remainingSeconds + "s");
            pvpTagScore.setScore(5);
        }
        
        // Linha em branco
        Score blankScore = objective.getScore(ChatColor.GRAY + "");
        blankScore.setScore(4);
        
        // Informações do servidor
        Score serverScore = objective.getScore(ChatColor.YELLOW + "Jogadores: " + ChatColor.WHITE + Bukkit.getOnlinePlayers().size());
        serverScore.setScore(3);
    }
    
    /**
     * Atualiza os scoreboards de todos os jogadores.
     */
    public static void updateAllScoreboards() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            updateScoreboard(player);
        }
    }
    
    /**
     * Remove o scoreboard de um jogador.
     * 
     * @param player O jogador
     */
    public static void removeScoreboard(Player player) {
        playerScoreboards.remove(player.getUniqueId());
    }
    
    /**
     * Inicia o sistema de scoreboard.
     * 
     * @param plugin A instância principal do plugin
     */
    public static void initialize(Main plugin) {
        // Criar scoreboards para jogadores online
        for (Player player : Bukkit.getOnlinePlayers()) {
            createScoreboard(player);
        }
        
        // Agendar atualização periódica dos scoreboards
        Bukkit.getScheduler().runTaskTimer(plugin, ScoreboardManager::updateAllScoreboards, 20L, 20L);
    }
}
