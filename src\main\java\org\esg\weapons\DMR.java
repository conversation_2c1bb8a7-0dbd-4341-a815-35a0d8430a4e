package org.esg.weapons;

import org.bukkit.entity.Player;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;

// Imports para o recoil visual
import org.bukkit.craftbukkit.v1_8_R3.entity.CraftPlayer;
import net.minecraft.server.v1_8_R3.PacketPlayOutPosition;

/**
 * DMR - Rifle semi-automático de 5 tiros rápidos com recoil visual.
 * Não ativa scope nem é tratada como sniper para listeners.
 */
public class DMR extends Weapon {
    public DMR() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis, MultiplicadorHeadshot
        super("DMR", WeaponType.RIFLE, AmmoType._762MM, 10, 160, 0.92, 3, 135, 5, 5, 2, 1, 1.8);
    }

    @Override
    public void shoot(Player player) {
        super.shoot(player);
        // Recoil visual: levanta a tela do jogador (pitch) sem mover a posição
        float yaw = player.getLocation().getYaw();
        float pitch = player.getLocation().getPitch();
        float newPitch = Math.max(pitch - 7.0f, -89.9f);
        // Altera apenas o pitch da visão do jogador, sem mexer em posição ou yaw
        org.bukkit.Location loc = player.getLocation();
        loc.setPitch(newPitch);
        player.teleport(loc);
    }

    // Sobrescreve o tipo para listeners não tratarem como sniper (sem scope, sem delay extra)
    @Override
    public WeaponType getType() {
        return WeaponType.RIFLE;
    }
} 