package org.esg.utils;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.util.Vector;

import java.util.concurrent.ThreadLocalRandom;

/**
 * Utility class for calculating vanilla-style knockback for weapon shots.
 * Ported from Exerosis' kotlin example to provide consistent knockback mechanics.
 */
public class KnockbackUtil {

    private static final double FRICTION = 1.0;
    private static final double HORIZONTAL = 0.25;      // Reduzido para diminuir movimento horizontal para trás
    private static final double VERTICAL = 0.27;        // Mantido para preservar knockback vertical
    private static final double EXTRA_VERTICAL = 0.075; // Mantido para preservar knockback vertical
    private static final double EXTRA_HORIZONTAL = 0.8; // Reduzido para diminuir movimento horizontal para trás

    /**
     * Calculates vanilla-style knockback vector for an entity being hit by an attacker.
     *
     * @param entityLoc The location of the entity being hit
     * @param attacker The entity causing the knockback (can be null)
     * @return A Vector representing the knockback force to apply
     */
    public static Vector getKnockback(Location entityLoc, Entity attacker) {
        return getKnockback(entityLoc, attacker, null);
    }

    /**
     * Calculates weapon-specific knockback vector for an entity being hit by an attacker.
     *
     * @param entityLoc The location of the entity being hit
     * @param attacker The entity causing the knockback (can be null)
     * @param weaponName The name of the weapon causing the knockback (null for default)
     * @return A Vector representing the knockback force to apply
     */
    public static Vector getKnockback(Location entityLoc, Entity attacker, String weaponName) {
        if(attacker == null) {
            return new Vector(0.0, -0.078375, 0.0);
        }

        // Get weapon-specific knockback modifiers
        KnockbackModifiers modifiers = getWeaponKnockbackModifiers(weaponName);

        ThreadLocalRandom random = ThreadLocalRandom.current();

        Location from = attacker.getLocation();

        double deltaX = entityLoc.getX() - from.getX();
        double deltaZ = entityLoc.getZ() - from.getZ();
        double y = VERTICAL * modifiers.verticalMultiplier;

        // --- SECTION START ---
        // Comment to add consistency and improve performance
        while (deltaX * deltaX + deltaZ * deltaZ < 1.0E-4) {
            deltaX = (random.nextDouble()) - random.nextDouble() * 0.01;
            deltaZ = (random.nextDouble()) - random.nextDouble() * 0.01;
        }
        // --- SECTION END ---

        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ) * FRICTION;
        deltaX = deltaX / distance * (HORIZONTAL * modifiers.horizontalMultiplier);
        deltaZ = deltaZ / distance * (HORIZONTAL * modifiers.horizontalMultiplier);

        // Check if attacker is a player and if they're crouching
        boolean isCrouching = false;
        if (attacker instanceof org.bukkit.entity.Player) {
            isCrouching = ((org.bukkit.entity.Player) attacker).isSneaking();
        }

        if(!isCrouching) {
            double yaw = from.getYaw() * Math.PI / 180;
            deltaX += -Math.sin(yaw) * (EXTRA_HORIZONTAL * modifiers.extraHorizontalMultiplier);
            deltaZ = Math.cos(yaw) * (EXTRA_HORIZONTAL * modifiers.extraHorizontalMultiplier);

            y += (EXTRA_VERTICAL * modifiers.extraVerticalMultiplier);
            deltaX *= 0.6;
            deltaZ *= 0.6;
        }

        // Apply weapon-specific final reduction
        deltaX *= modifiers.finalReduction;
        deltaZ *= modifiers.finalReduction;
        y *= modifiers.finalReduction;

        return new Vector(deltaX, y, deltaZ);
    }

    /**
     * Inner class to hold weapon-specific knockback modifiers
     */
    private static class KnockbackModifiers {
        double horizontalMultiplier = 1.0;
        double verticalMultiplier = 1.0;
        double extraHorizontalMultiplier = 1.0;
        double extraVerticalMultiplier = 1.0;
        double finalReduction = 0.85; // Default reduction

        KnockbackModifiers(double horizontal, double vertical, double extraHorizontal, double extraVertical, double reduction) {
            this.horizontalMultiplier = horizontal;
            this.verticalMultiplier = vertical;
            this.extraHorizontalMultiplier = extraHorizontal;
            this.extraVerticalMultiplier = extraVertical;
            this.finalReduction = reduction;
        }
    }

    /**
     * Gets weapon-specific knockback modifiers
     */
    private static KnockbackModifiers getWeaponKnockbackModifiers(String weaponName) {
        if (weaponName == null) {
            return new KnockbackModifiers(1.0, 1.0, 1.0, 1.0, 0.85);
        }

        switch (weaponName) {
            case "AR-15":
                // AR-15: Base values mais suaves para efeito gradual
                return new KnockbackModifiers(
                    0.9,  // Base horizontal mais suave
                    1.1,  // Base vertical mais suave (será aumentado gradualmente)
                    0.8,  // Base extra horizontal mais suave
                    1.2,  // Base extra vertical mais suave (será aumentado gradualmente)
                    0.85  // Redução padrão
                );
            case "AK-47":
                // AK-47: Standard automatic weapon knockback
                return new KnockbackModifiers(1.0, 1.0, 1.0, 1.0, 0.85);
            case "UZI":
                // UZI: Lighter knockback due to smaller caliber
                return new KnockbackModifiers(0.8, 0.9, 0.8, 0.9, 0.85);
            case "M4A1":
                // M4A1: Controlled burst knockback
                return new KnockbackModifiers(0.9, 1.1, 0.9, 1.1, 0.85);
            case "Barrett":
            case "Dragunov":
                // Snipers: Strong knockback
                return new KnockbackModifiers(1.4, 1.3, 1.4, 1.2, 0.9);
            case "Spas-12":
                // Shotgun: Heavy horizontal knockback
                return new KnockbackModifiers(1.3, 1.0, 1.5, 0.8, 0.9);
            default:
                // Default knockback for other weapons
                return new KnockbackModifiers(1.0, 1.0, 1.0, 1.0, 0.85);
        }
    }
}
