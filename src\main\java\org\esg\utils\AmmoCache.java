package org.esg.utils;

import de.tr7zw.nbtapi.NBTItem;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Classe utilitária que mantém um cache da munição das armas para evitar 
 * a atualização constante dos NBTs, o que causa animações indesejadas.
 */
public final class AmmoCache {
    
    // Estrutura de dados para armazenar a munição atual
    // Chave externa: UUID do jogador
    // Chave interna: ID da arma
    // Valor: Munição atual
    private static final Map<UUID, Map<String, Integer>> ammoCache = new HashMap<>();
    
    private AmmoCache() {
        // Construtor privado para evitar instanciação
    }
    
    /**
     * Obtém a munição atual de uma arma do cache.
     * Se não estiver no cache, lê do NBT do item e atualiza o cache.
     * 
     * @param player O jogador
     * @param item O item da arma
     * @return A munição atual, ou -1 se o item não for uma arma
     */
    public static int getAmmo(Player player, ItemStack item) {
        if (player == null || item == null) return -1;
        
        String weaponId = NBTUtils.getWeaponID(item);
        if (weaponId == null) return -1;
        
        UUID playerId = player.getUniqueId();
        
        // Verificar se já existe no cache
        if (ammoCache.containsKey(playerId)) {
            Map<String, Integer> playerCache = ammoCache.get(playerId);
            if (playerCache.containsKey(weaponId)) {
                return playerCache.get(weaponId);
            }
        }
        
        // Se não estiver no cache, ler do NBT e armazenar no cache
        NBTItem nbtItem = new NBTItem(item);
        if (!nbtItem.hasTag("current_ammo")) return -1;
        
        int currentAmmo = nbtItem.getInteger("current_ammo");
        setAmmo(player, weaponId, currentAmmo);
        
        return currentAmmo;
    }
    
    /**
     * Define a munição atual de uma arma no cache.
     * 
     * @param player O jogador
     * @param weaponId O ID da arma
     * @param ammo A nova quantidade de munição
     */
    public static void setAmmo(Player player, String weaponId, int ammo) {
        if (player == null || weaponId == null) return;
        
        UUID playerId = player.getUniqueId();
        
        // Garantir que o mapa para o jogador existe
        ammoCache.computeIfAbsent(playerId, k -> new HashMap<>());
        
        // Atualizar o cache
        ammoCache.get(playerId).put(weaponId, ammo);
    }
    
    /**
     * Define a munição atual de uma arma no cache.
     * 
     * @param player O jogador
     * @param item O item da arma
     * @param ammo A nova quantidade de munição
     * @return true se a atualização foi bem-sucedida, false caso contrário
     */
    public static boolean setAmmo(Player player, ItemStack item, int ammo) {
        if (player == null || item == null) return false;
        
        String weaponId = NBTUtils.getWeaponID(item);
        if (weaponId == null) return false;
        
        setAmmo(player, weaponId, ammo);
        return true;
    }
    
    /**
     * Decrementa a munição de uma arma no cache.
     * 
     * @param player O jogador
     * @param item O item da arma
     * @return A nova quantidade de munição, ou -1 em caso de erro
     */
    public static int decrementAmmo(Player player, ItemStack item) {
        if (player == null || item == null) return -1;
        
        int currentAmmo = getAmmo(player, item);
        if (currentAmmo <= 0) return 0;
        
        currentAmmo--;
        setAmmo(player, item, currentAmmo);
        
        return currentAmmo;
    }
    
    /**
     * Sincroniza o cache com o NBT do item.
     * Este método deve ser chamado apenas quando necessário:
     * - Quando o jogador troca de arma
     * - Quando o jogador recarrega completamente
     * - Quando o jogador desconecta
     * 
     * @param player O jogador
     * @param item O item da arma
     * @param forceUpdate Se true, força a atualização mesmo que não haja mudanças
     */
    public static void syncWithNBT(Player player, ItemStack item, boolean forceUpdate) {
        if (player == null || item == null) return;
        
        String weaponId = NBTUtils.getWeaponID(item);
        if (weaponId == null) return;
        
        int cachedAmmo = getAmmo(player, item);
        if (cachedAmmo < 0) return;
        
        // Usar o método direto da NBT API para minimizar qualquer possibilidade de animação
        NBTItem nbtItem = new NBTItem(item, true);
        int nbtAmmo = nbtItem.getInteger("current_ammo");
        
        // Só atualizar se houver diferença ou se forçado
        if (forceUpdate || cachedAmmo != nbtAmmo) {
            nbtItem.setInteger("current_ammo", cachedAmmo);
        }
    }
    
    /**
     * Remove os dados de cache de um jogador.
     * Deve ser chamado quando o jogador desconecta.
     * 
     * @param player O jogador
     */
    public static void removePlayer(Player player) {
        if (player == null) return;
        ammoCache.remove(player.getUniqueId());
    }
} 