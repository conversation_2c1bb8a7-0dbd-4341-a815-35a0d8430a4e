package org.esg.Manager;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.esg.models.Weapon;
import org.esg.utils.WeaponUtils;
import org.esg.weapons.WeaponFactory;
import java.util.logging.Logger;

public final class WeaponManager {
    private static final Logger LOGGER = Logger.getLogger(WeaponManager.class.getName());

    private WeaponManager() {}

    public static void giveWeapon(Player player, Weapon weapon) {
        if (player == null) {
            LOGGER.severe("giveWeapon: player é null");
            return;
        }
        
        if (weapon == null) {
            LOGGER.severe("giveWeapon: weapon é null para jogador " + player.getName());
            return;
        }
        
        LOGGER.info("Criando ItemStack para arma: " + weapon.getName());
        ItemStack baseItem = WeaponFactory.toItemStack(weapon);
        
        if (baseItem == null) {
            LOGGER.severe("Falha ao criar ItemStack para " + weapon.getName());
            return;
        }
        
        LOGGER.info("Base ItemStack criado com material: " + baseItem.getType());
        
        ItemStack item = WeaponUtils.applyWeaponToItem(baseItem, weapon, player);
        
        if (item == null) {
            LOGGER.severe("Falha ao aplicar NBT para " + weapon.getName());
            return;
        }
        
        LOGGER.info("NBT aplicado com sucesso, adicionando " + weapon.getName() + " ao inventário de " + player.getName());
        player.getInventory().addItem(item);
        LOGGER.info("Arma " + weapon.getName() + " adicionada com sucesso");
    }
    
    /**
     * Obtém a arma que o jogador está segurando atualmente.
     * @param player O jogador
     * @return A arma ativa, ou null se não estiver segurando uma arma
     */
    public static Weapon getActiveWeapon(Player player) {
        ItemStack itemInHand = player.getItemInHand();
        if (itemInHand == null) {
            return null;
        }
        
        return WeaponUtils.getWeaponFromItem(itemInHand, player);
    }
}