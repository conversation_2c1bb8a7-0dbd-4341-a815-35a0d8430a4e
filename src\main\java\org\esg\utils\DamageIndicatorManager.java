package org.esg.utils;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.esg.Main;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * Gerencia indicadores visuais de dano para melhorar o feedback ao jogador.
 */
public class DamageIndicatorManager {

    /**
     * Mostra um indicador direcional de dano ao jogador.
     * Indica a direção de onde veio o dano.
     *
     * @param player O jogador que recebeu dano
     * @param damageSource A localização da fonte do dano
     */
    public static void showDirectionalIndicator(Player player, Location damageSource) {
        // Calcular a direção relativa do dano
        Vector playerDirection = player.getLocation().getDirection();
        Vector damageDirection = damageSource.toVector().subtract(player.getLocation().toVector()).normalize();

        // Calcular o ângulo entre a direção do jogador e a direção do dano
        double dot = playerDirection.dot(damageDirection);
        double angle = Math.acos(dot);

        // Determinar se o dano veio da esquerda ou direita
        Vector cross = playerDirection.getCrossProduct(damageDirection);
        boolean fromRight = cross.getY() < 0;

        // Determinar a posição do indicador
        String indicator;
        if (angle < Math.PI / 4) { // Frente (45 graus)
            indicator = "§c↑";
        } else if (angle > 3 * Math.PI / 4) { // Trás (45 graus)
            indicator = "§c↓";
        } else if (fromRight) { // Direita
            indicator = "§c→";
        } else { // Esquerda
            indicator = "§c←";
        }

        // Mostrar o indicador
        MessageHandler.sendActionBar(player, indicator);
    }

    /**
     * Mostra um alerta quando o jogador está com pouca vida.
     *
     * @param player O jogador
     * @param damage Quantidade de dano recebido
     */
    public static void showLowHealthEffect(Player player, double damage) {
        double health = player.getHealth() - damage;

        if (health <= 6.0) { // 3 corações ou menos
            // Mensagem de alerta
            MessageHandler.sendActionBar(player, "§4§l❤ LOW HEALTH ❤");
        }
    }
}
