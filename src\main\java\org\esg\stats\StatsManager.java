package org.esg.stats;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.esg.Main;
import org.esg.enums.WeaponType;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * Gerencia as estatísticas de todos os jogadores.
 */
public class StatsManager {

    private static final Map<UUID, PlayerStats> playerStats = new HashMap<>();
    private static File statsFile;
    private static FileConfiguration statsConfig;

    /**
     * Inicializa o gerenciador de estatísticas.
     *
     * @param plugin A instância principal do plugin
     */
    public static void initialize(Main plugin) {
        // Criar o diretório de dados se não existir
        if (!plugin.getDataFolder().exists()) {
            plugin.getDataFolder().mkdir();
        }

        // Configurar o arquivo de estatísticas
        statsFile = new File(plugin.getDataFolder(), "stats.yml");

        if (!statsFile.exists()) {
            try {
                statsFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().log(Level.SEVERE, "Não foi possível criar o arquivo de estatísticas", e);
            }
        }

        statsConfig = YamlConfiguration.loadConfiguration(statsFile);

        // Carregar estatísticas de todos os jogadores
        loadAllStats();

        // Agendar salvamento automático
        Bukkit.getScheduler().runTaskTimer(plugin, StatsManager::saveAllStats, 6000L, 6000L); // A cada 5 minutos
    }

    /**
     * Carrega as estatísticas de todos os jogadores do arquivo.
     */
    private static void loadAllStats() {
        if (statsConfig.getConfigurationSection("players") == null) {
            return;
        }

        for (String uuidString : statsConfig.getConfigurationSection("players").getKeys(false)) {
            UUID uuid = UUID.fromString(uuidString);
            String name = statsConfig.getString("players." + uuidString + ".name");

            PlayerStats stats = new PlayerStats(uuid, name);

            // Carregar estatísticas básicas
            stats.addKill(statsConfig.getInt("players." + uuidString + ".kills", 0));
            stats.addDeath(statsConfig.getInt("players." + uuidString + ".deaths", 0));
            stats.addHeadshot(statsConfig.getInt("players." + uuidString + ".headshots", 0));
            stats.addShotFired(statsConfig.getInt("players." + uuidString + ".shotsFired", 0));
            stats.addShotHit(statsConfig.getInt("players." + uuidString + ".shotsHit", 0));

            // Carregar estatísticas por arma
            stats.addRifleKill(statsConfig.getInt("players." + uuidString + ".rifleKills", 0));
            stats.addShotgunKill(statsConfig.getInt("players." + uuidString + ".shotgunKills", 0));
            stats.addSniperKill(statsConfig.getInt("players." + uuidString + ".sniperKills", 0));
            stats.addSmgKill(statsConfig.getInt("players." + uuidString + ".smgKills", 0));
            stats.addPistolKill(statsConfig.getInt("players." + uuidString + ".pistolKills", 0));

            // Carregar estatísticas de headshots
            stats.addRifleHeadshot(statsConfig.getInt("players." + uuidString + ".rifleHeadshots", 0));
            stats.addShotgunHeadshot(statsConfig.getInt("players." + uuidString + ".shotgunHeadshots", 0));
            stats.addSniperHeadshot(statsConfig.getInt("players." + uuidString + ".sniperHeadshots", 0));
            stats.addSmgHeadshot(statsConfig.getInt("players." + uuidString + ".smgHeadshots", 0));
            stats.addPistolHeadshot(statsConfig.getInt("players." + uuidString + ".pistolHeadshots", 0));

            // Carregar estatísticas de tiros
            stats.addRifleShot(statsConfig.getInt("players." + uuidString + ".rifleShots", 0));
            stats.addShotgunShot(statsConfig.getInt("players." + uuidString + ".shotgunShots", 0));
            stats.addSniperShot(statsConfig.getInt("players." + uuidString + ".sniperShots", 0));
            stats.addSmgShot(statsConfig.getInt("players." + uuidString + ".smgShots", 0));
            stats.addPistolShot(statsConfig.getInt("players." + uuidString + ".pistolShots", 0));

            playerStats.put(uuid, stats);
        }
    }

    /**
     * Salva as estatísticas de todos os jogadores no arquivo.
     */
    public static void saveAllStats() {
        for (PlayerStats stats : playerStats.values()) {
            String uuidString = stats.getPlayerUUID().toString();

            // Salvar informações básicas
            statsConfig.set("players." + uuidString + ".name", stats.getPlayerName());

            // Salvar estatísticas básicas
            statsConfig.set("players." + uuidString + ".kills", stats.getKills());
            statsConfig.set("players." + uuidString + ".deaths", stats.getDeaths());
            statsConfig.set("players." + uuidString + ".headshots", stats.getHeadshots());
            statsConfig.set("players." + uuidString + ".shotsFired", stats.getShotsFired());
            statsConfig.set("players." + uuidString + ".shotsHit", stats.getShotsHit());

            // Salvar estatísticas por arma
            statsConfig.set("players." + uuidString + ".rifleKills", stats.getRifleKills());
            statsConfig.set("players." + uuidString + ".shotgunKills", stats.getShotgunKills());
            statsConfig.set("players." + uuidString + ".sniperKills", stats.getSniperKills());
            statsConfig.set("players." + uuidString + ".smgKills", stats.getSmgKills());
            statsConfig.set("players." + uuidString + ".pistolKills", stats.getPistolKills());

            // Salvar estatísticas de headshots
            statsConfig.set("players." + uuidString + ".rifleHeadshots", stats.getRifleHeadshots());
            statsConfig.set("players." + uuidString + ".shotgunHeadshots", stats.getShotgunHeadshots());
            statsConfig.set("players." + uuidString + ".sniperHeadshots", stats.getSniperHeadshots());
            statsConfig.set("players." + uuidString + ".smgHeadshots", stats.getSmgHeadshots());
            statsConfig.set("players." + uuidString + ".pistolHeadshots", stats.getPistolHeadshots());

            // Salvar estatísticas de tiros
            statsConfig.set("players." + uuidString + ".rifleShots", stats.getRifleShots());
            statsConfig.set("players." + uuidString + ".shotgunShots", stats.getShotgunShots());
            statsConfig.set("players." + uuidString + ".sniperShots", stats.getSniperShots());
            statsConfig.set("players." + uuidString + ".smgShots", stats.getSmgShots());
            statsConfig.set("players." + uuidString + ".pistolShots", stats.getPistolShots());
        }

        try {
            statsConfig.save(statsFile);
        } catch (IOException e) {
            Bukkit.getLogger().log(Level.SEVERE, "Não foi possível salvar o arquivo de estatísticas", e);
        }
    }

    /**
     * Obtém as estatísticas de um jogador.
     *
     * @param player O jogador
     * @return As estatísticas do jogador
     */
    public static PlayerStats getPlayerStats(Player player) {
        return getPlayerStats(player.getUniqueId(), player.getName());
    }

    /**
     * Obtém as estatísticas de um jogador pelo UUID.
     *
     * @param uuid O UUID do jogador
     * @param name O nome do jogador (usado se as estatísticas não existirem)
     * @return As estatísticas do jogador
     */
    public static PlayerStats getPlayerStats(UUID uuid, String name) {
        PlayerStats stats = playerStats.get(uuid);

        if (stats == null) {
            stats = new PlayerStats(uuid, name);
            playerStats.put(uuid, stats);
        }

        return stats;
    }

    /**
     * Registra um tiro disparado por um jogador.
     *
     * @param player O jogador
     * @param weaponType O tipo de arma
     */
    public static void registerShotFired(Player player, WeaponType weaponType) {
        PlayerStats stats = getPlayerStats(player);
        stats.addShotFired(weaponType);
    }

    /**
     * Registra um tiro acertado por um jogador.
     *
     * @param player O jogador
     */
    public static void registerShotHit(Player player) {
        PlayerStats stats = getPlayerStats(player);
        stats.addShotHit();
    }

    /**
     * Registra um kill feito por um jogador.
     *
     * @param player O jogador
     * @param weaponType O tipo de arma
     * @param isHeadshot Se o kill foi um headshot
     */
    public static void registerKill(Player player, WeaponType weaponType, boolean isHeadshot) {
        PlayerStats stats = getPlayerStats(player);
        stats.addKill(weaponType);

        // Não registramos o headshot aqui para evitar contagem dupla
        // O headshot já foi registrado quando o jogador acertou o tiro
    }

    /**
     * Registra um headshot feito por um jogador, independentemente de resultar em kill.
     *
     * @param player O jogador
     * @param weaponType O tipo de arma
     */
    public static void registerHeadshot(Player player, WeaponType weaponType) {
        PlayerStats stats = getPlayerStats(player);
        stats.addHeadshot(weaponType);
    }

    /**
     * Registra uma morte de um jogador.
     *
     * @param player O jogador
     */
    public static void registerDeath(Player player) {
        PlayerStats stats = getPlayerStats(player);
        stats.addDeath();
    }

    /**
     * Obtém o top N jogadores por kills.
     *
     * @param n O número de jogadores a retornar
     * @return Uma lista dos top N jogadores
     */
    public static List<PlayerStats> getTopKills(int n) {
        return playerStats.values().stream()
                .sorted(Comparator.comparingInt(PlayerStats::getKills).reversed())
                .limit(n)
                .collect(Collectors.toList());
    }

    /**
     * Obtém o top N jogadores por K/D ratio.
     *
     * @param n O número de jogadores a retornar
     * @return Uma lista dos top N jogadores
     */
    public static List<PlayerStats> getTopKDRatio(int n) {
        return playerStats.values().stream()
                .sorted(Comparator.comparingDouble(PlayerStats::getKDRatio).reversed())
                .limit(n)
                .collect(Collectors.toList());
    }

    /**
     * Obtém o top N jogadores por headshots.
     *
     * @param n O número de jogadores a retornar
     * @return Uma lista dos top N jogadores
     */
    public static List<PlayerStats> getTopHeadshots(int n) {
        return playerStats.values().stream()
                .sorted(Comparator.comparingInt(PlayerStats::getHeadshots).reversed())
                .limit(n)
                .collect(Collectors.toList());
    }

    /**
     * Obtém o top N jogadores por precisão.
     *
     * @param n O número de jogadores a retornar
     * @return Uma lista dos top N jogadores
     */
    public static List<PlayerStats> getTopAccuracy(int n) {
        return playerStats.values().stream()
                .filter(stats -> stats.getShotsFired() >= 100) // Mínimo de 100 tiros para evitar outliers
                .sorted(Comparator.comparingDouble(PlayerStats::getAccuracy).reversed())
                .limit(n)
                .collect(Collectors.toList());
    }

    /**
     * Formata uma mensagem de estatísticas para um jogador.
     *
     * @param player O jogador
     * @return A mensagem formatada
     */
    public static List<String> formatPlayerStats(Player player) {
        PlayerStats stats = getPlayerStats(player);
        List<String> lines = new ArrayList<>();

        lines.add(ChatColor.GOLD + "=== Estatísticas de " + player.getName() + " ===");
        lines.add(ChatColor.YELLOW + "Kills: " + ChatColor.WHITE + stats.getKills());
        lines.add(ChatColor.YELLOW + "Mortes: " + ChatColor.WHITE + stats.getDeaths());
        lines.add(ChatColor.YELLOW + "K/D Ratio: " + ChatColor.WHITE + String.format("%.2f", stats.getKDRatio()));
        lines.add(ChatColor.YELLOW + "Headshots: " + ChatColor.WHITE + stats.getHeadshots() +
                 " (" + String.format("%.1f", stats.getHeadshotRate()) + "%)");
        lines.add(ChatColor.YELLOW + "Precisão: " + ChatColor.WHITE + String.format("%.1f", stats.getAccuracy()) + "%");
        lines.add(ChatColor.YELLOW + "Tiros Disparados: " + ChatColor.WHITE + stats.getShotsFired());
        lines.add(ChatColor.YELLOW + "Tiros Acertados: " + ChatColor.WHITE + stats.getShotsHit());

        lines.add(ChatColor.GOLD + "--- Kills por Arma ---");
        lines.add(ChatColor.YELLOW + "Rifle: " + ChatColor.WHITE + stats.getRifleKills());
        lines.add(ChatColor.YELLOW + "Shotgun: " + ChatColor.WHITE + stats.getShotgunKills());
        lines.add(ChatColor.YELLOW + "Sniper: " + ChatColor.WHITE + stats.getSniperKills());
        lines.add(ChatColor.YELLOW + "SMG: " + ChatColor.WHITE + stats.getSmgKills());
        lines.add(ChatColor.YELLOW + "Pistola: " + ChatColor.WHITE + stats.getPistolKills());

        lines.add(ChatColor.GOLD + "--- Headshots por Arma ---");
        lines.add(ChatColor.YELLOW + "Rifle: " + ChatColor.WHITE + stats.getRifleHeadshots());
        lines.add(ChatColor.YELLOW + "Shotgun: " + ChatColor.WHITE + stats.getShotgunHeadshots());
        lines.add(ChatColor.YELLOW + "Sniper: " + ChatColor.WHITE + stats.getSniperHeadshots());
        lines.add(ChatColor.YELLOW + "SMG: " + ChatColor.WHITE + stats.getSmgHeadshots());
        lines.add(ChatColor.YELLOW + "Pistola: " + ChatColor.WHITE + stats.getPistolHeadshots());

        return lines;
    }

    /**
     * Formata uma mensagem de leaderboard.
     *
     * @param title O título do leaderboard
     * @param stats A lista de jogadores
     * @param valueExtractor Uma função para extrair o valor a ser exibido
     * @return A mensagem formatada
     */
    public static List<String> formatLeaderboard(String title, List<PlayerStats> stats,
                                               java.util.function.Function<PlayerStats, String> valueExtractor) {
        List<String> lines = new ArrayList<>();

        lines.add(ChatColor.GOLD + "=== " + title + " ===");

        for (int i = 0; i < stats.size(); i++) {
            PlayerStats playerStat = stats.get(i);
            lines.add(ChatColor.YELLOW + "#" + (i + 1) + " " +
                     ChatColor.WHITE + playerStat.getPlayerName() + ": " +
                     ChatColor.GREEN + valueExtractor.apply(playerStat));
        }

        return lines;
    }
}
