package org.esg.utils;

import org.bukkit.Bukkit;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.stats.StatsManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Utilitário para gerenciar estatísticas de armas.
 */
public class StatsUtil {

    // Mapa para rastrear o último dano causado por um jogador
    private static final Map<UUID, DamageInfo> lastDamage = new HashMap<>();

    /**
     * Registra um dano causado por um jogador a uma entidade.
     * Este método deve ser chamado sempre que um jogador causa dano a uma entidade com uma arma.
     *
     * @param attacker O jogador que causou o dano
     * @param victim A entidade que recebeu o dano
     * @param weapon A arma usada
     * @param isHeadshot Se o dano foi um headshot
     */
    public static void registerWeaponDamage(Player attacker, LivingEntity victim, Weapon weapon, boolean isHeadshot) {
        // Registrar o tiro acertado
        StatsManager.registerShotHit(attacker);

        // Armazenar informações sobre o dano para uso posterior (em caso de morte)
        lastDamage.put(victim.getUniqueId(), new DamageInfo(attacker.getUniqueId(), weapon.getType(), weapon.getName(), isHeadshot));
    }

    /**
     * Registra a morte de uma entidade.
     * Este método deve ser chamado sempre que uma entidade morre.
     *
     * @param victim A entidade que morreu
     */
    public static void registerEntityDeath(LivingEntity victim) {
        if (!(victim instanceof Player)) {
            return; // Apenas registrar mortes de jogadores
        }

        Player victimPlayer = (Player) victim;

        // Registrar a morte do jogador
        StatsManager.registerDeath(victimPlayer);

        // Verificar se o jogador foi morto por outro jogador
        DamageInfo damageInfo = lastDamage.get(victim.getUniqueId());

        if (damageInfo != null) {
            Player killer = getPlayerFromUUID(damageInfo.getKillerUUID());

            if (killer != null) {
                // Registrar o kill
                StatsManager.registerKill(killer, damageInfo.getWeaponType(), damageInfo.isHeadshot());

                // Obter o nome da arma
                String weaponName = damageInfo.getWeaponName();

                // Enviar mensagem de kill para todos os jogadores
                String headshot = damageInfo.isHeadshot() ? " §4§l[HEADSHOT]" : "";
                String killMessage = "§e" + killer.getName() + " §c§lmatou §e" + victimPlayer.getName() + " §c§lusando §6§l" + weaponName + headshot;
                Bukkit.broadcastMessage(killMessage);
            }

            // Remover as informações de dano
            lastDamage.remove(victim.getUniqueId());
        }
    }

    /**
     * Obtém um jogador a partir do UUID.
     */
    private static Player getPlayerFromUUID(UUID uuid) {
        return org.bukkit.Bukkit.getPlayer(uuid);
    }

    /**
     * Classe para armazenar informações sobre o último dano causado a uma entidade.
     */
    private static class DamageInfo {
        private final UUID killerUUID;
        private final WeaponType weaponType;
        private final String weaponName;
        private final boolean headshot;

        public DamageInfo(UUID killerUUID, WeaponType weaponType, String weaponName, boolean headshot) {
            this.killerUUID = killerUUID;
            this.weaponType = weaponType;
            this.weaponName = weaponName;
            this.headshot = headshot;
        }

        public UUID getKillerUUID() {
            return killerUUID;
        }

        public WeaponType getWeaponType() {
            return weaponType;
        }

        public String getWeaponName() {
            return weaponName;
        }

        public boolean isHeadshot() {
            return headshot;
        }
    }
}
