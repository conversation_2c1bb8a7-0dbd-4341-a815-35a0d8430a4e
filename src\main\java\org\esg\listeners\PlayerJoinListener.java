package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.ItemStack;
import org.esg.ui.EnhancedHUD;
import org.esg.utils.AmmoCache;
import org.esg.utils.NBTUtils;
import org.esg.models.Weapon;

/**
 * Listener para eventos de entrada de jogadores no servidor.
 * Inicia o HUD aprimorado para cada jogador que entra.
 */
public class PlayerJoinListener implements Listener {
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Iniciar o HUD aprimorado para o jogador
        EnhancedHUD.startHUD(player);
        
        // Inicializar o cache de munição para todas as armas no inventário
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null) {
                Weapon weapon = NBTUtils.getWeaponFromNBT(item, player);
                if (weapon != null) {
                    // Ler a munição do NBT e atualizar o cache
                    AmmoCache.setAmmo(player, item, weapon.getCurrentAmmo());
                }
            }
        }
    }
}
