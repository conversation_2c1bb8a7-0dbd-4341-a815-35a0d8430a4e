@echo off
echo ===================================
echo = Compilando e instalando o plugin =
echo ===================================

rem Compilar o projeto
echo Compilando o projeto...
call mvn clean package

if %ERRORLEVEL% neq 0 (
    echo Erro ao compilar o projeto.
    echo Pressione qualquer tecla para sair...
    pause > nul
    exit /b 1
)

echo Compilação concluída com sucesso!

rem Verificar se o servidor está rodando
tasklist /FI "IMAGENAME eq java.exe" | find "java.exe" > nul
if %ERRORLEVEL% == 0 (
    echo O servidor está rodando. O plugin será copiado e recarregado.
) else (
    echo O servidor não está rodando. O plugin será apenas copiado.
)

rem Copiar o plugin para a pasta do servidor
echo Copiando o plugin para a pasta do servidor...
copy /Y "target\esgotoserver-1.0.jar" "C:\Users\<USER>\Desktop\Server\plugins\esgotoserver.jar"

if %ERRORLEVEL% neq 0 (
    echo Erro ao copiar o plugin para a pasta do servidor.
    echo Pressione qualquer tecla para sair...
    pause > nul
    exit /b 1
)

echo Plugin copiado com sucesso!

rem Tentar recarregar o plugin se o servidor estiver rodando
tasklist /FI "IMAGENAME eq java.exe" | find "java.exe" > nul
if %ERRORLEVEL% == 0 (
    echo Tentando recarregar o plugin no servidor...

    rem Tentar enviar comando para o console do servidor
    echo reload > reload_command.txt
    type reload_command.txt > \\.\pipe\minecraft_server 2>nul

    if %ERRORLEVEL% == 0 (
        echo Plugin recarregado com sucesso!
    ) else (
        echo Não foi possível enviar o comando de recarga automaticamente.
        echo Use o comando /reload no console do servidor para recarregar o plugin.
    )
)

echo ===================================
echo = Processo concluído com sucesso! =
echo ===================================
echo.
echo Pressione qualquer tecla para sair...
pause > nul
