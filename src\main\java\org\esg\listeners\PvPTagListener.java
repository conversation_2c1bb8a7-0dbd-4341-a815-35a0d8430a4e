package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.esg.Manager.PvPTagManager;
import org.esg.Manager.ScoreboardManager;

/**
 * Listener para eventos relacionados ao sistema de PvP tag.
 */
public class PvPTagListener implements Listener {

    /**
     * Aplica a tag de PvP quando um jogador é atingido por outro jogador.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerDamage(EntityDamageByEntityEvent event) {
        // Verificar se o dano foi entre jogadores
        if (!(event.getEntity() instanceof Player) || !(event.getDamager() instanceof Player)) {
            return;
        }

        Player victim = (Player) event.getEntity();

        // Qualquer dano entre jogadores ativa o PvP tag
        // Não precisamos verificar se foi causado por uma arma

        // Aplicar a tag de PvP apenas para a vítima
        PvPTagManager.tagPlayer(victim);

        // Atualizar o scoreboard da vítima
        ScoreboardManager.updateScoreboard(victim);
    }

    /**
     * Método para comandos durante o PvP tag.
     * Anteriormente bloqueava comandos, mas agora permite todos os comandos.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerCommand(PlayerCommandPreprocessEvent event) {
        // Todos os comandos são permitidos durante o PvP tag
        // Este método foi mantido para possíveis implementações futuras
    }

    /**
     * Mata jogadores que deslogam durante o PvP tag.
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Verificar se o jogador está em tag de PvP
        PvPTagManager.handleCombatLogout(player);
    }
}
