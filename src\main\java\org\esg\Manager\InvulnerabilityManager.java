package org.esg.Manager;

import org.bukkit.entity.LivingEntity;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

/**
 * Gerencia o tempo de invulnerabilidade das entidades após receberem dano.
 */
public class InvulnerabilityManager {
    private static final Map<UUID, Integer> invulTicks = new HashMap<>();
    public static final int DEFAULT_INVUL_TICKS = 10; // Padrão do Minecraft: 10 ticks

    /**
     * Define o tempo de invulnerabilidade para uma entidade.
     */
    public static void setInvulTicks(LivingEntity entity, int ticks) {
        invulTicks.put(entity.getUniqueId(), ticks);
    }

    /**
     * Decrementa o contador de invulnerabilidade para todas as entidades.
     * Remove entidades que não estão mais invulneráveis.
     */
    public static void decrementInvulTicks() {
        Iterator<Map.Entry<UUID, Integer>> iterator = invulTicks.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<UUID, Integer> entry = iterator.next();
            int current = entry.getValue();
            if (current <= 1) {
                iterator.remove();
            } else {
                entry.setValue(current - 1);
            }
        }
    }

    /**
     * Verifica se uma entidade pode receber dano.
     */
    public static boolean canTakeDamage(LivingEntity entity) {
        return !invulTicks.containsKey(entity.getUniqueId()) || invulTicks.get(entity.getUniqueId()) <= 0;
    }

    /**
     * Reseta o tempo de invulnerabilidade de uma entidade.
     */
    public static void resetInvulTicks(LivingEntity entity, int ticks) {
        invulTicks.put(entity.getUniqueId(), ticks);
    }
}