@echo off
echo Compilando o plugin...
call mvn clean package

if %ERRORLEVEL% neq 0 (
    echo Erro ao compilar o plugin.
    pause
    exit /b 1
)

echo Copiando o plugin para a pasta do servidor...
copy /Y "target\esgotoserver-1.0.jar" "C:\Users\<USER>\Desktop\Server\plugins\esgotoserver.jar"

if %ERRORLEVEL% neq 0 (
    echo Erro ao copiar o plugin para a pasta do servidor.
    pause
    exit /b 1
)

echo Plugin compilado e copiado com sucesso!
echo Use o comando /reload no servidor para recarregar o plugin.
echo.
echo Agora você pode usar o comando /weapon shotgun para obter a shotgun.
echo.
pause
