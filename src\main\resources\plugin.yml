name: esgotoserver
version: '${project.version}'
main: org.esg.Main
depend: [NBTAPI, ActionBarAPI]
commands:
  weapon:
    description: Dá uma arma ao jogador
    usage: /<command> [tipo] [jogador]
  stats:
    description: Mostra estatísticas de jogadores
    usage: /<command> [jogador]
    aliases: [estatisticas, estatísticas]
  armor:
    description: Dá uma armadura ao jogador
    usage: /<command> <leather|kevlar> [jogador]
    aliases: [armadura]
  safezone:
    description: Gerencia áreas seguras
    usage: /<command> [create|delete|list|pos1|pos2|info] [nome]
    aliases: [sz, zonasegura]
  knockback:
    description: Gerencia o sistema de imunidade ao knockback
    usage: /<command> [status|reload|enable|disable|check <player>]
    aliases: [kb, knockbackimmunity]