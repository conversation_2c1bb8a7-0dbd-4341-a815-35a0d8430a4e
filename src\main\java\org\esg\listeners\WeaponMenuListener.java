package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.ItemStack;
import org.esg.ui.WeaponMenu;

import java.util.UUID;

/**
 * Listener para eventos relacionados ao menu de armas.
 */
public class WeaponMenuListener implements Listener {
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        
        // Verificar se o inventário clicado é o menu de armas
        if (WeaponMenu.isWeaponMenu(event.getInventory())) {
            // Cancelar o evento para evitar que o jogador pegue os itens
            event.setCancelled(true);
            
            // Processar o clique no menu
            ItemStack clickedItem = event.getCurrentItem();
            WeaponMenu.handleMenuClick(player, clickedItem);
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        
        // Verificar se o inventário fechado é o menu de armas
        if (WeaponMenu.isWeaponMenu(event.getInventory())) {
            // Remover o jogador da lista de menus abertos
            WeaponMenu.removePlayer(player.getUniqueId());
        }
    }
}
