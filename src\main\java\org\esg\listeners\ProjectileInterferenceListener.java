package org.esg.listeners;

import org.bukkit.entity.Arrow;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.ProjectileLaunchEvent;

/**
 * Listener para prevenir interferência de projéteis (especialmente flechas) 
 * com o sistema de knockback das armas customizadas.
 */
public class ProjectileInterferenceListener implements Listener {

    /**
     * Cancela eventos de dano causados por flechas para evitar interferência
     * com o sistema de knockback das armas customizadas.
     */
    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = false)
    public void onProjectileDamage(EntityDamageByEntityEvent event) {
        // Cancelar qualquer dano causado por flechas ou projéteis para evitar interferência
        if (event.getDamager() instanceof Arrow || event.getDamager() instanceof Projectile) {
            // Verificar se foi um player que atirou a flecha
            if (event.getDamager() instanceof Arrow) {
                Arrow arrow = (Arrow) event.getDamager();
                if (arrow.getShooter() instanceof Player) {
                    Player shooter = (Player) arrow.getShooter();
                    // Se o player atirou uma flecha em si mesmo, cancelar completamente
                    if (event.getEntity().equals(shooter)) {
                        event.setCancelled(true);
                        return;
                    }
                }
            }
            
            // Cancelar todos os outros eventos de projéteis para evitar interferência
            event.setCancelled(true);
        }
    }

    /**
     * Previne o lançamento de flechas por players para evitar interferência
     * com o sistema de armas customizadas.
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onProjectileLaunch(ProjectileLaunchEvent event) {
        // Cancelar lançamento de flechas por players para evitar interferência
        if (event.getEntity() instanceof Arrow && event.getEntity().getShooter() instanceof Player) {
            event.setCancelled(true);
        }
    }
}
