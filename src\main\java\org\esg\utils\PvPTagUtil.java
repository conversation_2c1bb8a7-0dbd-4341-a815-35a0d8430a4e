package org.esg.utils;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.esg.Manager.PvPTagManager;
import org.esg.Manager.ScoreboardManager;

/**
 * Utilitário para gerenciar o PvP tag.
 */
public class PvPTagUtil {

    /**
     * Aplica a tag de PvP quando um jogador causa dano a outro jogador.
     * Este método deve ser chamado sempre que um jogador causa dano a outro jogador.
     *
     * @param attacker O jogador que causou o dano
     * @param victim A entidade que recebeu o dano
     */
    public static void applyPvPTag(Player attacker, LivingEntity victim) {
        // Verificar se a vítima é um jogador
        if (!(victim instanceof Player)) {
            return;
        }

        Player victimPlayer = (Player) victim;

        // Aplicar a tag de PvP apenas para a vítima
        PvPTagManager.tagPlayer(victimPlayer);

        // Atualizar o scoreboard da vítima
        ScoreboardManager.updateScoreboard(victimPlayer);
    }
}
