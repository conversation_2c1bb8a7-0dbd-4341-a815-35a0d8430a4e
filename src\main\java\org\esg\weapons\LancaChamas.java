package org.esg.weapons;

import org.bukkit.Sound;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.util.Vector;
import org.esg.enums.AmmoType;
import org.esg.models.Weapon;
import org.esg.enums.WeaponType;
import org.esg.Main;

import java.util.HashSet;
import java.util.Set;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class LancaChamas extends Weapon implements Listener {

    private static final int FIRE_DURATION_TICKS = 80; // Reduzido de 120 para 80 (4 segundos)
    private static final double FIRE_DAMAGE_PER_TICK = 1.5; // Aumentado para compensar menos ticks
    private static final int FIRE_DAMAGE_INTERVAL = 40; // A cada 2 segundos

    // Limite de alvos por disparo
    private static final int MAX_TARGETS_PER_SHOT = 2; // Reduzido de 3 para 2

    // Conjunto para rastrear entidades recentemente atingidas
    private final Set<Integer> recentlyHitEntities = new HashSet<>();

    // Limites para reduzir processamento
    private static final int MAX_ACTIVE_FIRES = 8;
    private static int currentActiveFires = 0;

    public LancaChamas() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis, Spread, ProgressionRate, Randomness, HeadshotMultiplier, DamageSpread
        super("Lança-Chamas", WeaponType.SMG, AmmoType._12GAUGE, 0.5, 5, 1.0, 5.0, 10, 50, 50, 5, 1,
              1.0, 1.0, 1.5, 1.0, 0.15);

        // Taxa de disparo reduzida de 10.0 para 5.0
        // Número de projéteis reduzido de 2 para 1
        // Spread reduzido de 1.5 para 1.0
        // Progression e Randomness reduzidos

        // Registrar como listener para detectar acertos de projéteis
        Main.getPlugin().getServer().getPluginManager().registerEvents(this, Main.getPlugin());

        // Limpar caches periodicamente - intervalo aumentado para 15 segundos
        new BukkitRunnable() {
            @Override
            public void run() {
                limparCaches();
            }
        }.runTaskTimer(Main.getPlugin(), 300L, 300L); // A cada 15 segundos (300 ticks)
    }

    private void limparCaches() {
        recentlyHitEntities.clear();
    }

    @Override
    public void shoot(Player player) {
        // Verificar limite de fogos ativos no servidor
        if (currentActiveFires >= MAX_ACTIVE_FIRES) {
            // Apenas efeito básico sem processamento completo
            player.getWorld().playSound(player.getLocation(), Sound.FIRE, 0.5F, 0.5F);
            super.shoot(player);
            return;
        }

        // Efeitos sonoros - apenas um som em vez de múltiplos
        player.getWorld().playSound(player.getLocation(), Sound.FIRE, 0.7F, 0.5F);

        // Encontrar e queimar alvos
        encontrarEQueimarAlvos(player);

        // Comportamento normal de disparo
        super.shoot(player);
    }

    // Sistema otimizado para encontrar e queimar alvos
    private void encontrarEQueimarAlvos(Player player) {
        Location eyeLocation = player.getEyeLocation();
        Vector direction = eyeLocation.getDirection().normalize();
        double maxRange = this.range * 0.8; // Redução de 20% no alcance para performance

        // Usar valor mais alto para raio de busca para compensar imprecisões
        double searchRadius = maxRange + 1.0;
        int alvosEncontrados = 0;

        // Busca simplificada de alvos
        for (Entity entity : player.getNearbyEntities(searchRadius, searchRadius, searchRadius)) {
            // Limitar alvos processados
            if (alvosEncontrados >= MAX_TARGETS_PER_SHOT) break;

            if (entity instanceof LivingEntity && entity != player) {
                LivingEntity target = (LivingEntity) entity;

                // Verificação de ID para ignorar alvos recém-atingidos
                int entityId = target.getEntityId();
                if (recentlyHitEntities.contains(entityId)) continue;

                // Verificação rápida de distância
                double distance = target.getLocation().distance(eyeLocation);
                if (distance <= maxRange) {
                    // Verificação de ângulo simplificada - apenas dot product
                    Vector toTarget = target.getLocation().toVector()
                                    .subtract(eyeLocation.toVector())
                                    .normalize();
                    double dot = toTarget.dot(direction);

                    // Verificação rápida - dot > 0.7 = ~45 graus (mais amplo)
                    if (dot > 0.7) {
                        // Verificação de linha de visão apenas se as outras passarem
                        if (player.hasLineOfSight(target)) {
                            aplicarFogo(player, target);
                            alvosEncontrados++;
                        }
                    }
                }
            }
        }
    }

    // Método otimizado para detectar acertos diretos
    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        // Usar baixa prioridade para processamento mais rápido

        // Verificações rápidas para saída antecipada
        if (!(event.getDamager() instanceof Player)) return;
        if (!(event.getEntity() instanceof LivingEntity)) return;

        Player player = (Player) event.getDamager();
        LivingEntity target = (LivingEntity) event.getEntity();

        // Verificação rápida de distância
        if (player.getLocation().distance(target.getLocation()) > this.range * 1.5) return;

        // Verificar arma ativa - apenas se todas as verificações anteriores passarem
        Weapon currentWeapon = org.esg.Manager.WeaponManager.getActiveWeapon(player);
        if (currentWeapon instanceof LancaChamas) {
            aplicarFogo(player, target);
        }
    }

    // Método ultra-otimizado para aplicar fogo
    private void aplicarFogo(Player atacante, LivingEntity alvo) {
        // Verificar limite global de fogos ativos
        if (currentActiveFires >= MAX_ACTIVE_FIRES) return;

        // Verificação de alvo já atingido
        int entityId = alvo.getEntityId();
        if (recentlyHitEntities.contains(entityId)) return;

        // Registrar o alvo e incrementar contador
        recentlyHitEntities.add(entityId);
        currentActiveFires++;

        // Aplicar fogo com duração reduzida
        alvo.setFireTicks(FIRE_DURATION_TICKS);

        // Mensagem simples para jogadores
        if (alvo instanceof Player) {
            ((Player) alvo).sendMessage("§c§lVOCÊ ESTÁ PEGANDO FOGO!");
        }

        // Aplicar dano em intervalos - agora com apenas 2 verificações em vez de 3
        aplicarDanoPeriodico(atacante, alvo);

        // Decrementar contador global após a duração do fogo
        new BukkitRunnable() {
            @Override
            public void run() {
                currentActiveFires--;
                recentlyHitEntities.remove(entityId);
            }
        }.runTaskLater(Main.getPlugin(), FIRE_DURATION_TICKS);
    }

    // Método otimizado para aplicar dano periódico
    private void aplicarDanoPeriodico(Player atacante, LivingEntity alvo) {
        // Reduzido para apenas 2 ticks de dano em vez de 3

        // 1º tick de dano - após 2 segundos (40 ticks)
        new BukkitRunnable() {
            @Override
            public void run() {
                if (alvo.isValid() && !alvo.isDead()) {
                    alvo.damage(FIRE_DAMAGE_PER_TICK, atacante);
                    alvo.setFireTicks(Math.max(alvo.getFireTicks(), 40)); // Manter o fogo
                }
            }
        }.runTaskLater(Main.getPlugin(), FIRE_DAMAGE_INTERVAL);

        // 2º e último tick de dano - após 4 segundos (80 ticks)
        new BukkitRunnable() {
            @Override
            public void run() {
                if (alvo.isValid() && !alvo.isDead()) {
                    alvo.damage(FIRE_DAMAGE_PER_TICK, atacante);
                }
            }
        }.runTaskLater(Main.getPlugin(), FIRE_DAMAGE_INTERVAL * 2);
    }
}