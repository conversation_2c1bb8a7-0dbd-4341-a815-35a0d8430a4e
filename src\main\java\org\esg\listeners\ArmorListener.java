package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.esg.Manager.ArmorManager;
import org.esg.enums.ArmorPiece;

/**
 * Listener para eventos relacionados a armaduras.
 */
public class ArmorListener implements Listener {
    
    /**
     * Monitora quando o jogador interage com os slots de armadura no inventário.
     * Agora permite remover naturalmente, mantendo o sistema informado.
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        
        // Se for um slot de armadura e estiver removendo uma peça
        if (event.getSlotType() == InventoryType.SlotType.ARMOR) {
            // Não vamos mais cancelar o evento, mas informar o sistema que a armadura foi removida
            
            // Determinando qual peça foi removida
            int slot = event.getRawSlot();
            ArmorPiece piece = null;
            
            // Mapeamento dos slots de armadura do Minecraft (podem variar em diferentes versões)
            switch (slot) {
                case 5: // Capacete no inventário padrão
                case 39: // Capacete no inventário player.getInventory()
                    piece = ArmorPiece.HELMET;
                    break;
                case 6: // Peitoral
                case 38:
                    piece = ArmorPiece.CHESTPLATE;
                    break;
                case 7: // Calças
                case 37:
                    piece = ArmorPiece.LEGGINGS;
                    break;
                case 8: // Botas
                case 36:
                    piece = ArmorPiece.BOOTS;
                    break;
            }
            
            // Se identificamos a peça, notificar o sistema
            if (piece != null) {
                // Usamos o MONITOR para deixar o evento ocorrer normalmente
                // e apenas atualizamos nosso sistema depois
                ArmorManager.removeArmorPiece(player, piece);
            }
        }
    }
    
    /**
     * Limpa os dados de armadura quando o jogador sai.
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        ArmorManager.clearPlayerData(event.getPlayer());
    }
    
    /**
     * Gerencia o dano de queda e outros tipos de dano que não são de entidades.
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        // Ignorar dano de entidades, pois já é tratado no sistema de armas
        if (event.getCause() == EntityDamageEvent.DamageCause.ENTITY_ATTACK || 
            event.getCause() == EntityDamageEvent.DamageCause.PROJECTILE) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        double damage = event.getDamage();
        
        // Calcular o dano reduzido pela armadura
        double reducedDamage = ArmorManager.calculateReducedDamage(player, damage);
        
        // Aplicar o dano reduzido
        event.setDamage(reducedDamage);
    }
}
