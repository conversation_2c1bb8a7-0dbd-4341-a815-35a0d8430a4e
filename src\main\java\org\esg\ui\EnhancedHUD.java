package org.esg.ui;

import org.bukkit.ChatColor;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Main;
import org.esg.models.Weapon;
import org.esg.utils.ActionBarUtil;
import org.esg.utils.WeaponUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Gerencia a interface de usuário aprimorada para o sistema de armas.
 * Inclui HUD detalhado, indicador visual de recarga e indicador de dano.
 */
public class EnhancedHUD {

    // Armazena o tempo de início da recarga para cada jogador
    private static final Map<UUID, Long> reloadStartTimes = new HashMap<>();

    // Armazena o dano cumulativo causado por cada jogador
    private static final Map<UUID, Double> cumulativeDamage = new HashMap<>();

    // Armazena o tempo do último dano causado
    private static final Map<UUID, Long> lastDamageTime = new HashMap<>();

    // Armazena se o último dano foi um headshot
    private static final Map<UUID, Boolean> lastDamageWasHeadshot = new HashMap<>();

    // Armazena a última entidade atingida por cada jogador
    private static final Map<UUID, UUID> lastHitEntity = new HashMap<>();

    // Duração da exibição do indicador de dano (em ms)
    private static final long DAMAGE_INDICATOR_DURATION = 3000;

    // Tempo máximo entre danos para manter o contador cumulativo (em ms)
    private static final long DAMAGE_RESET_TIMEOUT = 3000;

    /**
     * Inicia o HUD para um jogador.
     * @param player O jogador
     */
    public static void startHUD(Player player) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!player.isOnline()) {
                    cancel();
                    return;
                }

                Weapon weapon = WeaponUtils.getWeaponFromItem(player.getInventory().getItemInHand(), player);
                if (weapon != null) {
                    updateHUD(player, weapon);
                }
            }
        }.runTaskTimer(Main.getPlugin(), 0L, 5L); // Atualiza a cada 5 ticks (1/4 de segundo)
    }

    /**
     * Atualiza o HUD para um jogador.
     * @param player O jogador
     * @param weapon A arma atual
     */
    private static void updateHUD(Player player, Weapon weapon) {
        StringBuilder hudMessage = new StringBuilder();

        // Obter a munição atual do cache em vez de usar o valor do objeto Weapon
        ItemStack itemInHand = player.getInventory().getItemInHand();
        int currentAmmo = itemInHand != null ? 
            org.esg.utils.AmmoCache.getAmmo(player, itemInHand) : 
            weapon.getCurrentAmmo();
        if (currentAmmo == -1) currentAmmo = weapon.getCurrentAmmo();

        if (weapon.isReloading(player)) {
            // Exibição de recarga elegante com segundos restantes e animação de pontos
            long reloadStartTime = reloadStartTimes.getOrDefault(player.getUniqueId(), System.currentTimeMillis());
            long currentTime = System.currentTimeMillis();
            long totalReloadTime = weapon.getReloadTime() * 1000;
            long elapsed = currentTime - reloadStartTime;
            double secondsLeft = Math.max(0, (totalReloadTime - elapsed) / 100) / 10.0; // 1 casa decimal
            long dots = (elapsed / 400) % 4;
            StringBuilder dotsStr = new StringBuilder();
            for (int i = 0; i < dots; i++) dotsStr.append(".");
            hudMessage.append("§bRecarregando §f(").append("§e").append(String.format("%.1f", secondsLeft)).append("s§f)").append(dotsStr);
        } else {
            // Exibição minimalista de munição
            hudMessage.append("§fAmmo §a").append(currentAmmo);
        }

        // Enviar a mensagem para a action bar usando o utilitário
        ActionBarUtil.sendActionBar(player, hudMessage.toString());
    }

    /**
     * Registra o início de uma recarga.
     * @param player O jogador
     */
    public static void startReload(Player player) {
        reloadStartTimes.put(player.getUniqueId(), System.currentTimeMillis());
    }

    /**
     * Registra o dano causado por um jogador a uma entidade específica.
     * @param player O jogador
     * @param target A entidade atingida
     * @param damage O dano causado
     * @param isHeadshot Se o dano foi causado por um headshot
     */
    public static void registerDamage(Player player, LivingEntity target, double damage, boolean isHeadshot) {
        UUID playerUUID = player.getUniqueId();
        UUID targetUUID = target.getUniqueId();
        long currentTime = System.currentTimeMillis();

        // Verificar se deve resetar o contador de dano cumulativo
        boolean shouldReset = false;

        // Verificar se passou muito tempo desde o último dano
        Long lastTime = lastDamageTime.get(playerUUID);
        if (lastTime != null && (currentTime - lastTime) > DAMAGE_RESET_TIMEOUT) {
            shouldReset = true;
        }

        // Verificar se a entidade atingida é diferente da última
        UUID lastTarget = lastHitEntity.get(playerUUID);
        if (lastTarget != null && !lastTarget.equals(targetUUID)) {
            shouldReset = true;
        }

        // Resetar ou acumular o dano
        if (shouldReset) {
            cumulativeDamage.put(playerUUID, damage);
        } else {
            // Acumular o dano
            double currentCumulative = cumulativeDamage.getOrDefault(playerUUID, 0.0);
            cumulativeDamage.put(playerUUID, currentCumulative + damage);
        }

        // Atualizar os outros dados
        lastDamageTime.put(playerUUID, currentTime);
        lastDamageWasHeadshot.put(playerUUID, isHeadshot);
        lastHitEntity.put(playerUUID, targetUUID);
    }

    /**
     * Registra o dano causado por um jogador (sem informação da entidade).
     * @param player O jogador
     * @param damage O dano causado
     * @param isHeadshot Se o dano foi causado por um headshot
     */
    public static void registerDamage(Player player, double damage, boolean isHeadshot) {
        // Usar um UUID aleatório para a entidade quando não temos essa informação
        // Isso fará com que o dano seja sempre acumulado
        UUID randomEntityUUID = UUID.randomUUID();
        registerDamage(player, player, damage, isHeadshot); // Usar o próprio jogador como alvo temporário
    }

    /**
     * Registra o dano causado por um jogador (sem headshot e sem informação da entidade).
     * @param player O jogador
     * @param damage O dano causado
     */
    public static void registerDamage(Player player, double damage) {
        registerDamage(player, damage, false);
    }

    /**
     * Atualiza o progresso da recarga para um jogador.
     * Este método é chamado pelo sistema de recarga para atualizar o progresso visual.
     *
     * @param player O jogador
     * @param progress O progresso da recarga (0.0 a 1.0)
     */
    public static void updateReloadProgress(Player player, float progress) {
        // Atualizar o tempo de início da recarga para simular o progresso correto
        UUID playerUUID = player.getUniqueId();
        Weapon weapon = WeaponUtils.getWeaponFromItem(player.getInventory().getItemInHand(), player);

        if (weapon != null) {
            long totalReloadTime = weapon.getReloadTime() * 1000;
            long currentTime = System.currentTimeMillis();
            long adjustedStartTime = currentTime - (long)(progress * totalReloadTime);

            // Atualizar o tempo de início para refletir o progresso atual
            reloadStartTimes.put(playerUUID, adjustedStartTime);
        }
    }
}
