package org.esg.ui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.utils.NBTUtils;
import org.esg.weapons.WeaponFactory;
import org.esg.Manager.WeaponManager;
import org.esg.weapons.AK47;
import org.esg.weapons.AR15;
import org.esg.weapons.Barrett;
import org.esg.weapons.Shotgun;
import org.esg.weapons.UZI;
import org.esg.weapons.Dragunov;
import org.esg.weapons.LancaChamas;
import org.esg.weapons.Oitao;
import org.esg.weapons.M4A1;
import org.esg.weapons.RPG;
import org.esg.weapons.DMR;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Gerencia o menu de seleção de armas.
 */
public class WeaponMenu {

    // Cores padronizadas para o menu
    private static final ChatColor TITLE_COLOR = ChatColor.DARK_RED;
    private static final ChatColor WEAPON_NAME_COLOR = ChatColor.GOLD;
    private static final ChatColor STAT_LABEL_COLOR = ChatColor.YELLOW;
    private static final ChatColor STAT_VALUE_COLOR = ChatColor.WHITE;
    private static final ChatColor INSTRUCTION_COLOR = ChatColor.GREEN;
    private static final ChatColor INFO_COLOR = ChatColor.GRAY;

    private static final String MENU_TITLE = TITLE_COLOR + "Seleção de Armas";
    private static final int MENU_SIZE = 36; // Aumentado para 4 linhas para acomodar as novas armas

    // Mapa para rastrear inventários abertos por jogador
    private static final Map<UUID, Inventory> openMenus = new HashMap<>();

    /**
     * Abre o menu de seleção de armas para um jogador.
     *
     * @param player O jogador
     */
    public static void openMenu(Player player) {
        // Criar o inventário
        Inventory menu = Bukkit.createInventory(null, MENU_SIZE, MENU_TITLE);

        // Adicionar as armas ao menu em um padrão visualmente elegante
        // Primeira linha - Rifles e Shotgun
        addWeaponToMenu(menu, new AK47(), 10);    // Primeira linha, segunda coluna
        addWeaponToMenu(menu, new AR15(), 12);    // Primeira linha, quarta coluna
        addWeaponToMenu(menu, new Shotgun(), 14); // Primeira linha, sexta coluna
        addWeaponToMenu(menu, new UZI(), 16);     // Primeira linha, oitava coluna
        
        // Segunda linha - Snipers e armas especiais
        addWeaponToMenu(menu, new Barrett(), 19);    // Segunda linha, primeira coluna
        addWeaponToMenu(menu, new Dragunov(), 21);   // Segunda linha, terceira coluna
        addWeaponToMenu(menu, new LancaChamas(), 23); // Segunda linha, quinta coluna
        addWeaponToMenu(menu, new Oitao(), 25);      // Segunda linha, sétima coluna
        
        // Terceira linha - Novas armas
        addWeaponToMenu(menu, new M4A1(), 28);       // Terceira linha, primeira coluna
        addWeaponToMenu(menu, new RPG(), 30);        // Terceira linha, terceira coluna
        addWeaponToMenu(menu, new DMR(), 32);        // Terceira linha, quinta coluna

        // Adicionar item de informação
        ItemStack infoItem = new ItemStack(Material.PAPER);
        ItemMeta infoMeta = infoItem.getItemMeta();
        infoMeta.setDisplayName(STAT_LABEL_COLOR + "Informações");
        List<String> infoLore = new ArrayList<>();
        infoLore.add(INFO_COLOR + "Clique em uma arma para selecioná-la.");
        infoLore.add(INFO_COLOR + "A arma será adicionada ao seu inventário.");
        infoMeta.setLore(infoLore);
        infoItem.setItemMeta(infoMeta);
        menu.setItem(4, infoItem);

        // Adicionar bordas decorativas
        ItemStack borderItem = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7); // Cinza
        ItemMeta borderMeta = borderItem.getItemMeta();
        borderMeta.setDisplayName(" ");
        borderItem.setItemMeta(borderMeta);

        for (int i = 0; i < MENU_SIZE; i++) {
            if (menu.getItem(i) == null) {
                menu.setItem(i, borderItem);
            }
        }

        // Abrir o menu para o jogador
        player.openInventory(menu);

        // Registrar o menu aberto
        openMenus.put(player.getUniqueId(), menu);

        // Reproduzir som de abertura do menu
        player.playSound(player.getLocation(), Sound.CHEST_OPEN, 0.5f, 1.0f);
    }

    /**
     * Adiciona uma arma ao menu.
     *
     * @param menu O inventário do menu
     * @param weapon A arma a ser adicionada
     * @param slot O slot onde a arma será colocada
     */
    private static void addWeaponToMenu(Inventory menu, Weapon weapon, int slot) {
        // Usar ItemStack gerado pelo WeaponFactory em vez de criar um novo com Material.IRON_HOE
        ItemStack weaponItem = WeaponFactory.toItemStack(weapon);
        ItemMeta weaponMeta = weaponItem.getItemMeta();

        // Definir o nome da arma sem itálico usando §r antes da cor
        weaponMeta.setDisplayName("§r" + WEAPON_NAME_COLOR + weapon.getName());

        // Adicionar descrição da arma
        List<String> lore = new ArrayList<>();
        lore.add("§r" + INFO_COLOR + "Tipo: " + getWeaponTypeDisplay(weapon.getType()));
        lore.add("§r" + STAT_LABEL_COLOR + "Dano: " + STAT_VALUE_COLOR + String.format("%.1f", weapon.getDamage()));
        lore.add("§r" + STAT_LABEL_COLOR + "Precisão: " + STAT_VALUE_COLOR + String.format("%.0f", weapon.getAccuracy() * 100) + "%");
        lore.add("§r" + STAT_LABEL_COLOR + "Alcance: " + STAT_VALUE_COLOR + (int) weapon.getRange() + " blocos");
        lore.add("§r" + STAT_LABEL_COLOR + "Munição: " + STAT_VALUE_COLOR + weapon.getMaxAmmo());
        lore.add("§r" + STAT_LABEL_COLOR + "Cadência: " + STAT_VALUE_COLOR + String.format("%.1f", weapon.getFireRate()) + " tiros/s");
        lore.add("");
        lore.add("§r" + INSTRUCTION_COLOR + "Clique para selecionar esta arma");

        weaponMeta.setLore(lore);
        weaponItem.setItemMeta(weaponMeta);

        // Adicionar o item ao menu
        menu.setItem(slot, weaponItem);
    }

    /**
     * Obtém uma exibição formatada do tipo de arma.
     *
     * @param type O tipo de arma
     * @return Uma string formatada para exibição
     */
    private static String getWeaponTypeDisplay(WeaponType type) {
        // Usar uma cor consistente para todos os tipos de arma
        ChatColor typeColor = ChatColor.GOLD;

        switch (type) {
            case RIFLE:
                return "§r" + typeColor + "Rifle";
            case SHOTGUN:
                return "§r" + typeColor + "Shotgun";
            case SNIPER:
                return "§r" + typeColor + "Sniper";
            case PISTOL:
                return "§r" + typeColor + "Pistola";
            case SMG:
                return "§r" + typeColor + "SMG";
            default:
                return "§r" + typeColor + type.name();
        }
    }

    /**
     * Verifica se um inventário é um menu de armas.
     *
     * @param inventory O inventário a ser verificado
     * @return true se for um menu de armas, false caso contrário
     */
    public static boolean isWeaponMenu(Inventory inventory) {
        return inventory != null && inventory.getTitle().equals(MENU_TITLE);
    }

    /**
     * Processa o clique em um item do menu.
     *
     * @param player O jogador que clicou
     * @param clickedItem O item clicado
     */
    public static void handleMenuClick(Player player, ItemStack clickedItem) {
        if (clickedItem == null || clickedItem.getType() == Material.STAINED_GLASS_PANE || clickedItem.getType() == Material.PAPER) {
            return; // Ignorar cliques em itens decorativos ou de informação
        }

        // Obter o nome da arma a partir do item clicado
        String weaponName = ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName());

        // Fechar o menu
        player.closeInventory();

        try {
            // Criar a arma diretamente usando o WeaponFactory
            Weapon weapon = WeaponFactory.createWeapon(weaponName);

            // Dar a arma ao jogador usando o WeaponManager
            WeaponManager.giveWeapon(player, weapon);

            // Reproduzir som de seleção
            player.playSound(player.getLocation(), Sound.LEVEL_UP, 0.5f, 1.2f);

            // Enviar mensagem de confirmação
            player.sendMessage("§r" + INSTRUCTION_COLOR + "Você selecionou a arma " + "§r" + WEAPON_NAME_COLOR + weaponName + "§r" + INSTRUCTION_COLOR + "!");
        } catch (Exception e) {
            // Em caso de erro, notificar o jogador
            player.sendMessage(ChatColor.RED + "Erro ao selecionar a arma: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Remove um jogador da lista de menus abertos.
     *
     * @param playerUUID O UUID do jogador
     */
    public static void removePlayer(UUID playerUUID) {
        openMenus.remove(playerUUID);
    }
}
