package org.esg.commands;

import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.esg.models.Weapon;
import org.esg.weapons.WeaponFactory;
import org.esg.Manager.WeaponManager;
import org.esg.ui.WeaponMenu;
import java.util.logging.Logger;

import java.util.stream.Collectors;

public class WeaponCommand implements CommandExecutor {

    private static final String USAGE_MESSAGE = ChatColor.YELLOW + "Uso: /weapon [nome-da-arma] ou /weapon give [jogador] [nome-da-arma]";
    private static final String ONLY_PLAYERS_MESSAGE = ChatColor.RED + "Apenas jogadores podem executar este comando!";
    private static final String UNKNOWN_WEAPON_MESSAGE = ChatColor.RED + "Arma desconhecida: %s";
    private static final String WEAPON_RECEIVED_MESSAGE = ChatColor.GREEN + "Você recebeu uma %s!";
    private static final String AVAILABLE_WEAPONS_MESSAGE = ChatColor.YELLOW + "Armas disponíveis: %s";
    private static final Logger LOGGER = Logger.getLogger(WeaponCommand.class.getName());

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!isPlayer(sender)) {
            sender.sendMessage(ONLY_PLAYERS_MESSAGE);
            return true;
        }

        Player player = (Player) sender;

        // Se não houver argumentos, abrir o menu de armas
        if (args.length == 0) {
            WeaponMenu.openMenu(player);
            return true;
        }

        // Se o primeiro argumento for "give", dar a arma ao jogador
        if (args.length >= 3 && args[0].equalsIgnoreCase("give")) {
            String playerName = args[1];
            String weaponName = args[2];

            Player targetPlayer = player.getServer().getPlayer(playerName);
            if (targetPlayer != null) {
                giveWeaponToPlayer(targetPlayer, weaponName);
            } else {
                player.sendMessage(ChatColor.RED + "Jogador não encontrado: " + playerName);
            }

            return true;
        }

        // Caso contrário, dar a arma ao jogador que executou o comando
        giveWeaponToPlayer(player, args[0]);
        return true;
    }

    private boolean isPlayer(CommandSender sender) {
        return sender instanceof Player;
    }

    private void showUsage(Player player) {
        player.sendMessage(USAGE_MESSAGE);
        player.sendMessage(String.format(AVAILABLE_WEAPONS_MESSAGE, getAvailableWeapons()));
    }

    private String getAvailableWeapons() {
        // Usa Stream para coletar as chaves (nomes das armas) do WEAPON_REGISTRY e juntá-las com vírgulas
        return WeaponFactory.getWeaponRegistry().keySet().stream()
                .collect(Collectors.joining(", "));
    }

    private void giveWeaponToPlayer(Player player, String weaponName) {
        LOGGER.info("Tentando dar arma '" + weaponName + "' ao jogador " + player.getName());
        
        // Tratar caso específico da Barrett com grafias diferentes
        if (weaponName.equalsIgnoreCase("BARRET") || 
            weaponName.equalsIgnoreCase("Barret") || 
            weaponName.equalsIgnoreCase("BARRETT")) {
            LOGGER.info("Normalizando nome da Barrett");
            weaponName = "Barrett";
        }
        
        try {
            Weapon weapon = WeaponFactory.createWeapon(weaponName);
            if (weapon == null) {
                player.sendMessage(ChatColor.RED + "Arma não encontrada: " + weaponName);
                LOGGER.warning("Weapon null: " + weaponName);
                return;
            }
            
            // Comentado para reduzir log spam
            // LOGGER.info("Arma criada: " + weapon.getName() + ", dando ao jogador " + player.getName());
            WeaponManager.giveWeapon(player, weapon);
            player.sendMessage(ChatColor.GREEN + "Você recebeu uma " + ChatColor.RESET + ChatColor.GREEN + weapon.getName() + "!");
            LOGGER.info("Arma " + weapon.getName() + " dada com sucesso ao jogador " + player.getName());
        } catch (Exception e) {
            LOGGER.severe("Erro ao dar arma ao jogador: " + e.getMessage());
            player.sendMessage(ChatColor.RED + "Erro ao dar arma: " + e.getMessage());
        }
    }
}