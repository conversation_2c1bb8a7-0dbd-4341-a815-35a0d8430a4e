package org.esg.enums;

/**
 * Tipos de armadura disponíveis no jogo.
 */
public enum ArmorType {
    LEATHER("Couro", 0.45),
    KEVLAR("Kevlar", 0.6);

    private final String displayName;
    private final double totalProtection;

    ArmorType(String displayName, double totalProtection) {
        this.displayName = displayName;
        this.totalProtection = totalProtection;
    }

    /**
     * Obtém o nome de exibição do tipo de armadura.
     *
     * @return O nome de exibição
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Obtém a proteção total fornecida pelo conjunto completo de armadura.
     *
     * @return A proteção total (0.0 a 1.0)
     */
    public double getTotalProtection() {
        return totalProtection;
    }

    /**
     * Obtém a proteção fornecida pelo capacete.
     *
     * @return A proteção do capacete
     */
    public double getHelmetProtection() {
        return totalProtection * 0.2; // 20% da proteção total
    }

    /**
     * Obtém a proteção fornecida pelo peitoral.
     *
     * @return A proteção do peitoral
     */
    public double getChestplateProtection() {
        return totalProtection * 0.4; // 40% da proteção total
    }

    /**
     * Obtém a proteção fornecida pelas calças.
     *
     * @return A proteção das calças
     */
    public double getLeggingsProtection() {
        return totalProtection * 0.3; // 30% da proteção total
    }

    /**
     * Obtém a proteção fornecida pelas botas.
     *
     * @return A proteção das botas
     */
    public double getBootsProtection() {
        return totalProtection * 0.1; // 10% da proteção total
    }
}
