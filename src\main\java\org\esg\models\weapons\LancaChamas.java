package org.esg.models.weapons;

import net.minecraft.server.v1_8_R3.EnumParticle;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.esg.Effects.ParticleCat;
import org.esg.Main;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;
import org.esg.utils.MessageHandler;
import org.esg.Effects.SoundEffects;
import org.esg.systems.VisualEffectsSystem;

/**
 * Lança-chamas - Uma arma de curto alcance que dispara chamas
 * 
 * Características:
 * - Baixo alcance
 * - <PERSON><PERSON> baixa precisão (alta dispersão)
 * - <PERSON><PERSON> médio-alto contínuo
 * - Efeitos visuais de fogo
 * - Capacidade grande de munição
 */
public class LancaChamas extends Weapon {
    
    public LancaChamas() {
        super(
            "Lança-chamas",   // nome 
            WeaponType.SMG,   // tipo (SMG é o mais próximo em termos de mecânica)
            AmmoType._556MM,  // tipo de munição (usando 5.56mm como padrão para SMG)
            4.0,              // dano por projétil (mais baixo, mas dispara muitos projéteis)
            15.0,             // alcance em blocos (muito reduzido)
            0.4,              // precisão (0.0 a 1.0) - baixa precisão para espalhar
            15.0,             // taxa de disparo (tiros por segundo) - muito rápido
            40.0,             // velocidade do projétil (lenta, para dar efeito de chamas)
            100,              // capacidade máxima
            100,              // munição inicial
            5,                // tempo de recarga (segundos)
            5,                // número de projéteis por tiro (muitos para criar efeito de espalhamento)
            0.0               // multiplicador de headshot (padrão, sem bônus para headshot)
        );
    }
    
    @Override
    public void shoot(Player player) {
        // Verificar se pode atirar
        if (!canShoot(player)) {
            if (isReloading(player)) {
                MessageHandler.sendReloading(player);
            } else {
                MessageHandler.sendNoAmmo(player);
                SoundEffects.playError(player);
            }
            return;
        }
        
        // Decrementar a munição
        ItemStack itemInHand = player.getInventory().getItemInHand();
        int newAmmo = org.esg.utils.AmmoCache.decrementAmmo(player, itemInHand);
        this.setCurrentAmmo(newAmmo);
        
        // Criar efeito de fogo no cano
        VisualEffectsSystem.createMuzzleFlash(player, getType());
        
        // Efeitos sonoros adicionais para o lança-chamas
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.FIRE, 0.8f, 0.8f);
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.FIZZ, 0.5f, 0.5f);
        
        // Criar projetis especiais de fogo
        createFlameProjectiles(player);
    }
    
    /**
     * Cria projéteis de chama especiais para o lança-chamas
     */
    private void createFlameProjectiles(Player player) {
        // Obter a posição da cabeça/olhos do jogador
        Location eyeLocation = player.getEyeLocation();
        // Obter a direção para onde o jogador está olhando
        Vector direction = eyeLocation.getDirection().normalize();
        
        // Reproduzir efeitos sonoros de fogo
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.FIRE, 0.8f, 0.8f);
        
        // Criar efeitos contínuos de fogo
        new BukkitRunnable() {
            final int maxDuration = 10; // duração em ticks
            int tickCount = 0;
            
            @Override
            public void run() {
                if (tickCount >= maxDuration) {
                    cancel();
                    return;
                }
                
                // Criar várias partículas para simular uma língua de fogo
                for (int i = 1; i <= 10; i++) {
                    double distance = i * 1.5; // aumentar a distância progressivamente
                    
                    // Adicionar alguma dispersão para simular chamas
                    double offsetX = (Math.random() - 0.5) * 0.2 * i;
                    double offsetY = (Math.random() - 0.5) * 0.2 * i;
                    double offsetZ = (Math.random() - 0.5) * 0.2 * i;
                    
                    Location flameLocation = eyeLocation.clone().add(
                            direction.clone().multiply(distance))
                            .add(offsetX, offsetY, offsetZ);
                    
                    // Partículas de fogo e fumaça
                    ParticleCat.sendParticle(EnumParticle.FLAME, flameLocation, 0.1f, 0.1f, 0.1f, 0.01f, 2);
                    ParticleCat.sendParticle(EnumParticle.SMOKE_LARGE, flameLocation, 0.1f, 0.1f, 0.1f, 0.01f, 1);
                    
                    // A cada 3 unidades, verificar impacto
                    if (i % 3 == 0) {
                        // Verificar colisão com entidades
                        for (Entity entity : player.getWorld().getNearbyEntities(flameLocation, 1.5, 1.5, 1.5)) {
                            if (entity instanceof LivingEntity && entity != player) {
                                LivingEntity target = (LivingEntity) entity;
                                
                                // Aplicar dano de fogo
                                target.setFireTicks(60); // 3 segundos de fogo
                                
                                // Aplicar dano direto (reduzido para compensar o dano do fogo)
                                double flameDamage = damage * 0.5;
                                target.damage(flameDamage, player);
                                
                                // Knockback suave
                                Vector knockbackDir = target.getLocation().toVector()
                                        .subtract(player.getLocation().toVector()).normalize();
                                knockbackDir.multiply(0.2);
                                knockbackDir.setY(0.1);
                                target.setVelocity(target.getVelocity().add(knockbackDir));
                                
                                // Efeito visual de impacto
                                ParticleCat.sendParticle(EnumParticle.LAVA, target.getLocation(), 0.2f, 0.2f, 0.2f, 0.01f, 3);
                            }
                        }
                    }
                }
                
                tickCount++;
            }
        }.runTaskTimer(Main.getPlugin(), 0L, 2L);
    }
} 